<?php
session_start();
include('../admin/lib/db_connection.php');
include('../../mail.php');
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

$otp = rand(1000, 9999);

$sel_phone = dbQuery("SELECT * FROM tabl_user WHERE email='" . $_REQUEST['email'] . "'");
$num1 = dbNumRows($sel_phone);
if ($num1 == 0) {
	echo 2;
	die();
} else {

	dbQuery("DELETE FROM tabl_login_otp WHERE email='" . $_REQUEST['email'] . "'");
	dbQuery("INSERT INTO tabl_login_otp SET otp='" . $otp . "',email='" . $_REQUEST['email'] . "'");


	$to = $_REQUEST['email'];
	// Subject
	$subject = 'Login OTP | Project Womaniya';
	// Message
	$message = '<html>
<body>
<h1>Project Womaniya</h1> 
<div class="text" style="padding: 0 3em;">
<p>Here is your otp for login:</p> 
<h2>' . $otp . '</h2>
<p>Regards<br/>
Team Project Womaniya</p>
</div>
</html>';
	// To send HTML mail, the Content-type header must be set
	$headers[] = 'MIME-Version: 1.0';
	$headers[] = 'Content-type: text/html; charset=iso-8859-1';
	// Additional headers
	$headers[] = 'From: ' . SITE . ' <' . EMAIL . '>';
	// Mail it
	mail($to, $subject, $message, implode("\r\n", $headers));
	sendmail($to, $subject, $message);




	$_SESSION['login_email'] = $_REQUEST['email'];
	echo 1;
}
