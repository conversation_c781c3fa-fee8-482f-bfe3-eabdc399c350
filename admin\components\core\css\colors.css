/*  "COLORS" STYLES */
.text-default {
    color: #c3bedc;
}

.text-primary {
    color: #1b55e3;
}

.text-secondary {
    color: #6a7a84;
}

.text-success {
    color: #46be8a;
}

.text-danger {
    color: #fb434a;
}

.text-warning {
    color: #f39834;
}

.text-info {
    color: #0887c9;
}

.text-light {
    color: #f2f4f8;
}

.text-dark {
    color: #161537;
}

.text-white {
    color: #fff;
}

.text-muted {
    color: #aca6cc !important;
}

.text-blue {
    color: #1b55e3;
}

.text-blue-light {
    color: #3d6ee7;
}

.text-red {
    color: #f00;
}

.text-yellow {
    color: #ff0;
}

.text-orange {
    color: #f2a654;
}

.text-gray-1 {
    color: #f2f4f8;
}

.text-gray-2 {
    color: #e4e9f0;
}

.text-gray-3 {
    color: #dde2ec;
}

.text-gray-4 {
    color: #c3bedc;
}

.text-gray-5 {
    color: #aca6cc;
}

.text-gray-6 {
    color: #786fa4;
}

.bg-default {
    background-color: #c3bedc !important;
}

.bg-primary {
    background-color: #1b55e3 !important;
}

.bg-secondary {
    background-color: #1b55e3 !important;
}

.bg-success {
    background-color: #46be8a !important;
}

.bg-danger {
    background-color: #fb434a !important;
}

.bg-warning {
    background-color: #f39834 !important;
}

.bg-info {
    background-color: #0887c9 !important;
}

.bg-light {
    background-color: #f2f4f8 !important;
}

.bg-dark {
    background-color: #161537 !important;
}

.bg-white {
    background-color: #fff !important;
}

.bg-blue {
    background-color: #1b55e3 !important;
}

.bg-blue-light {
    background-color: #3d6ee7 !important;
}

.bg-red {
    background-color: #f00 !important;
}

.bg-yellow {
    background-color: #ff0 !important;
}

.bg-orange {
    background-color: #f2a654 !important;
}

.bg-gray-1 {
    background-color: #f2f4f8 !important;
}

.bg-gray-2 {
    background-color: #e4e9f0 !important;
}

.bg-gray-3 {
    background-color: #dde2ec !important;
}

.bg-gray-4 {
    background-color: #c3bedc !important;
}

.bg-gray-5 {
    background-color: #aca6cc !important;
}

.bg-gray-6 {
    background-color: #786fa4 !important;
}

.border-default {
    border-color: #c3bedc !important;
}

.border-primary {
    border-color: #1b55e3 !important;
}

.border-secondary {
    border-color: #1b55e3 !important;
}

.border-success {
    border-color: #46be8a !important;
}

.border-danger {
    border-color: #fb434a !important;
}

.border-warning {
    border-color: #f39834 !important;
}

.border-info {
    border-color: #0887c9 !important;
}

.border-light {
    border-color: #f2f4f8 !important;
}

.border-dark {
    border-color: #161537 !important;
}

.border-white {
    border-color: #fff !important;
}

.border-blue {
    border-color: #1b55e3 !important;
}

.border-blue-light {
    border-color: #3d6ee7 !important;
}

.border-red {
    border-color: #f00 !important;
}

.border-yellow {
    border-color: #ff0 !important;
}

.border-orange {
    border-color: #f2a654 !important;
}

.border-gray-1 {
    border-color: #f2f4f8 !important;
}

.border-gray-2 {
    border-color: #e4e9f0 !important;
}

.border-gray-3 {
    border-color: #dde2ec !important;
}

.border-gray-4 {
    border-color: #c3bedc !important;
}

.border-gray-5 {
    border-color: #aca6cc !important;
}

.border-gray-6 {
    border-color: #786fa4 !important;
}
