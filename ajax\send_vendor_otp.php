<?php

session_start();

include('../admin/lib/db_connection.php');
include('../../mail.php');

date_default_timezone_set("Asia/Kolkata");

$date = date('Y-m-d H:i:s');



$otp = rand(1000, 9999);

$sel_email = dbQuery("SELECT * FROM tabl_delivery_vendor WHERE email='" . $_REQUEST['email'] . "'");

$num1 = dbNumRows($sel_email);

if ($num1 > 0) {

  echo 2;

  die();
} else {



  $sel_phone = dbQuery("SELECT * FROM tabl_delivery_vendor WHERE phone='" . $_REQUEST['phone'] . "'");

  $num2 = dbNumRows($sel_phone);

  if ($num2 > 0) {

    echo 3;

    die();
  } else {


    dbQuery("DELETE FROM tabl_vendor_otp WHERE email='" . $_REQUEST['email'] . "'");
    dbQuery("DELETE FROM tabl_temp_vendor WHERE email='" . $_REQUEST['email'] . "'");

    dbQuery("INSERT INTO tabl_vendor_otp SET otp='" . $otp . "',email='" . $_REQUEST['email'] . "'");

    dbQuery("INSERT INTO tabl_temp_vendor SET name='" . mysqli_real_escape_string($con, $_REQUEST['name']) . "',phone='" . $_REQUEST['phone'] . "',email='" . $_REQUEST['email'] . "',password='" . md5($_REQUEST['password']) . "',city_id='" . $_REQUEST['city_id'] . "',date_added='" . $date . "'");

    // Send OTP via email
    $to = $_REQUEST['email'];
    $subject = 'Vendor Registration OTP | Project Womaniya';
    $message = '<html>
      <body>
        <h1>Project Womaniya</h1>
        <div class="text" style="padding: 0 3em;">
          <h2>Vendor Registration OTP</h2>
          <p>Hello ' . mysqli_real_escape_string($con, $_REQUEST['name']) . ',<br/>
          Here is your OTP for vendor registration:</p>
          <h2 style="color: #007bff; font-size: 32px; text-align: center; background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">' . $otp . '</h2>
          <p><strong>Note:</strong> This OTP is valid for 10 minutes only.</p>
          <p>If you did not request this OTP, please ignore this email.</p>
          <p>Regards<br/>
          Team Project Womaniya</p>
        </div>
      </body>
    </html>';

    sendmail($to, $subject, $message);

    $_SESSION['vendor_login_email'] = $_REQUEST['email'];

    echo 1;
  }
}
