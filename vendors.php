<?php
session_start();
include('admin/lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");
$date=date('Y-m-d H:i:s');

// Get filter parameters
$city_filter = isset($_GET['city']) ? $_GET['city'] : '';
$search_query = isset($_GET['search']) ? $_GET['search'] : '';
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <title>Find Vendors - Project Womaniya</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, minimal-ui, viewport-fit=cover">
  <meta name="theme-color" content="#009688">
  <link rel="shortcut icon" type="image/x-icon" href="assets/images/favicon.png">
  <link rel="stylesheet" type="text/css" href="assets/css/style.css">
  <link rel="preconnect" href="https://fonts.googleapis.com/">
  <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&amp;family=Poppins:wght@200;300;400;500;600;700;800;900&amp;display=swap" rel="stylesheet">

  <style>
    .vendor-card {
      background: white;
      border-radius: 15px;
      padding: 20px;
      margin-bottom: 15px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
      transition: transform 0.2s, box-shadow 0.2s;
      border: 1px solid #f0f0f0;
    }

    .vendor-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0,0,0,0.15);
      border-color: #007bff;
    }

    .vendor-avatar {
      width: 70px;
      height: 70px;
      border-radius: 50%;
      object-fit: cover;
      border: 3px solid #007bff;
      flex-shrink: 0;
    }

    .vendor-info {
      flex-grow: 1;
      min-width: 0;
    }

    .vendor-info h5 {
      margin: 0 0 8px 0;
      color: #333;
      font-weight: 600;
      font-size: 18px;
      line-height: 1.3;
    }

    .vendor-location {
      color: #666;
      font-size: 14px;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
    }

    .vendor-location i {
      margin-right: 6px;
      color: #007bff;
    }

    .vendor-contact {
      color: #007bff;
      font-size: 14px;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      padding: 6px 12px;
      border-radius: 20px;
      background: #f8f9fa;
      margin-right: 8px;
      margin-bottom: 8px;
      transition: all 0.2s;
    }

    .vendor-contact:hover {
      background: #007bff;
      color: white;
      text-decoration: none;
    }

    .vendor-contact i {
      margin-right: 5px;
    }

    .vendor-status {
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
      display: inline-flex;
      align-items: center;
    }

    .status-active {
      background: #d4edda;
      color: #155724;
    }

    .status-inactive {
      background: #f8d7da;
      color: #721c24;
    }

    .vendor-actions {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 12px;
      flex-wrap: wrap;
      gap: 8px;
    }

    .vendor-contacts {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .vendor-meta {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .search-section {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 25px;
      border-radius: 15px;
      margin-bottom: 20px;
      color: white;
    }

    .search-form {
      display: flex;
      flex-direction: column;
      gap: 15px;
      margin-top: 20px;
    }

    @media (min-width: 768px) {
      .search-form {
        flex-direction: row;
        gap: 12px;
      }
    }

    .search-input {
      flex: 1;
      padding: 15px 20px;
      border: none;
      border-radius: 30px;
      font-size: 16px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      min-height: 50px;
      box-sizing: border-box;
    }

    .search-input:focus {
      outline: none;
      box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }

    .city-input {
      flex: 1;
      padding: 15px 20px;
      border: 1px solid rgba(255,255,255,0.3);
      border-radius: 30px;
      font-size: 16px;
      background: rgba(255,255,255,0.9);
      color: #333;
      min-height: 50px;
      box-sizing: border-box;
    }

    .city-input:focus {
      outline: none;
      background: white;
      box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }

    .city-input::placeholder {
      color: #666;
    }

    .search-btn {
      background: white;
      color: #667eea;
      border: none;
      padding: 15px 25px;
      border-radius: 30px;
      font-weight: 600;
      cursor: pointer;
      font-size: 16px;
      min-height: 50px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
    }

    .search-btn:hover {
      background: #f8f9fa;
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }

    @media (max-width: 767px) {
      .search-btn {
        width: 100%;
      }
    }

    .filter-tags {
      margin-top: 15px;
    }

    .filter-tag {
      display: inline-block;
      background: rgba(255,255,255,0.2);
      padding: 5px 12px;
      border-radius: 15px;
      margin-right: 8px;
      font-size: 12px;
      cursor: pointer;
    }

    .filter-tag.active {
      background: white;
      color: #667eea;
    }

    .no-vendors {
      text-align: center;
      padding: 40px;
      color: #666;
    }

    .vendor-count {
      color: #666;
      margin-bottom: 15px;
      font-size: 14px;
    }
  </style>
</head>

<body>
  <div class="page-wraper">
    <!-- Preloader -->
    <div id="preloader">
      <div class="loader">
        <div class="load-circle">
          <div></div>
          <div></div>
        </div>
      </div>
    </div>

    <!-- Header -->
    <header class="header">
      <div class="main-bar">
        <div class="container">
          <div class="header-content">
            <div class="left-content">
              <a href="javascript:history.back()" class="back-btn">
                <i class="fa-solid fa-arrow-left"></i>
              </a>
              <h4 class="title mb-0 text-nowrap">Find Vendors</h4>
            </div>
            <div class="mid-content"></div>
            <div class="right-content">
              <a href="cart.php" class="item-content item-link">
                <i class="fa-solid fa-shopping-cart"></i>
              </a>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Page Content -->
    <div class="page-content">
      <div class="container">

        <!-- Search Section -->
        <div class="search-section">
          <h5><i class="fa-solid fa-search me-2"></i>Find Vendors Near You</h5>
          <p style="margin: 5px 0;">Discover local vendors and their services</p>

          <form class="search-form" method="GET">
            <input type="text" name="search" class="search-input" placeholder="Search vendors by name..." value="<?php echo htmlspecialchars($search_query); ?>">

            <input type="text" name="city" class="city-input" placeholder="Search by city name..." value="<?php echo htmlspecialchars($city_filter); ?>" id="city-search">

            <button type="submit" class="search-btn">
              <i class="fa-solid fa-search me-1"></i>Search
            </button>
          </form>

          <?php if($city_filter || $search_query): ?>
          <div class="filter-tags">
            <small>Active Filters:</small>
            <?php if($search_query): ?>
            <span class="filter-tag active">
              Search: "<?php echo htmlspecialchars($search_query); ?>"
              <a href="?city=<?php echo $city_filter; ?>" style="color: inherit; margin-left: 5px;">×</a>
            </span>
            <?php endif; ?>
            <?php if($city_filter): ?>
            <span class="filter-tag active">
              City: "<?php echo htmlspecialchars($city_filter); ?>"
              <a href="?search=<?php echo urlencode($search_query); ?>" style="color: inherit; margin-left: 5px;">×</a>
            </span>
            <?php endif; ?>
            <a href="vendors.php" class="filter-tag" style="color: inherit; text-decoration: none;">Clear All</a>
          </div>
          <?php endif; ?>
        </div>

        <!-- Vendors List -->
        <?php
        // Build query based on filters
        $where_conditions = array("tabl_delivery_vendor.status = 1");

        if($city_filter) {
          $city_escaped = mysqli_real_escape_string($con, $city_filter);
          $where_conditions[] = "(tabl_city.city LIKE '%".$city_escaped."%' OR tabl_state.state_name LIKE '%".$city_escaped."%')";
        }

        if($search_query) {
          $search_escaped = mysqli_real_escape_string($con, $search_query);
          $where_conditions[] = "(tabl_delivery_vendor.name LIKE '%".$search_escaped."%' OR tabl_delivery_vendor.email LIKE '%".$search_escaped."%')";
        }

        $where_clause = implode(" AND ", $where_conditions);

        $vendors_query = dbQuery("SELECT tabl_delivery_vendor.*, tabl_city.city, tabl_state.state_name
                                FROM tabl_delivery_vendor
                                LEFT JOIN tabl_city ON tabl_delivery_vendor.city_id = tabl_city.id
                                LEFT JOIN tabl_state ON tabl_city.state_id = tabl_state.id
                                WHERE ".$where_clause."
                                ORDER BY tabl_delivery_vendor.name ASC");

        $vendor_count = dbNumRows($vendors_query);
        ?>

        <div class="vendor-count">
          <i class="fa-solid fa-store me-1"></i>
          <?php echo $vendor_count; ?> vendor<?php echo ($vendor_count != 1) ? 's' : ''; ?> found
        </div>

        <?php if($vendor_count > 0): ?>
          <?php while($vendor = dbFetchAssoc($vendors_query)): ?>
          <div class="vendor-card" onclick="window.location.href='vendor-profile.php?id=<?php echo $vendor['id']; ?>'" style="cursor: pointer;">
            <div class="d-flex align-items-start gap-3">
              <img src="vendor/components/core/img/avatars/thumb-50/<?php echo !empty($vendor['user_image']) ? $vendor['user_image'] : 'default.png'; ?>"
                   alt="<?php echo htmlspecialchars($vendor['name']); ?>"
                   class="vendor-avatar">

              <div class="vendor-info">
                <h5><?php echo htmlspecialchars($vendor['name']); ?></h5>

                <?php if(!empty($vendor['city'])): ?>
                <div class="vendor-location">
                  <i class="fa-solid fa-map-marker-alt"></i>
                  <?php echo $vendor['city'].', '.$vendor['state_name']; ?>
                </div>
                <?php endif; ?>

                <div class="vendor-actions">
                  <div class="vendor-contacts">
                    <?php if(!empty($vendor['phone'])): ?>
                    <a href="tel:<?php echo $vendor['phone']; ?>" class="vendor-contact" onclick="event.stopPropagation();">
                      <i class="fa-solid fa-phone"></i>
                      <?php echo $vendor['phone']; ?>
                    </a>
                    <?php endif; ?>

                    <?php if(!empty($vendor['email'])): ?>
                    <a href="mailto:<?php echo $vendor['email']; ?>" class="vendor-contact" onclick="event.stopPropagation();">
                      <i class="fa-solid fa-envelope"></i>
                      Email
                    </a>
                    <?php endif; ?>
                  </div>

                  <div class="vendor-meta">
                    <span class="vendor-status <?php echo ($vendor['status'] == 1) ? 'status-active' : 'status-inactive'; ?>">
                      <i class="fa-solid fa-circle" style="font-size: 8px; margin-right: 5px;"></i>
                      <?php echo ($vendor['status'] == 1) ? 'Active' : 'Inactive'; ?>
                    </span>
                    <i class="fa-solid fa-chevron-right" style="color: #ccc; font-size: 12px;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <?php endwhile; ?>
        <?php else: ?>
          <div class="no-vendors">
            <i class="fa-solid fa-store" style="font-size: 48px; color: #ddd; margin-bottom: 15px;"></i>
            <h5>No Vendors Found</h5>
            <p>Try adjusting your search criteria or browse all vendors.</p>
            <?php if($city_filter || $search_query): ?>
            <a href="vendors.php" class="btn btn-primary mt-3">View All Vendors</a>
            <?php endif; ?>
          </div>
        <?php endif; ?>

      </div>
    </div>
  </div>

  <script src="assets/js/jquery.js"></script>
  <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="assets/js/settings.js"></script>
  <script src="assets/js/custom.js"></script>

  <script>
    // City search autocomplete functionality
    $(document).ready(function() {
      let cityTimeout;

      $('#city-search').on('input', function() {
        const query = $(this).val();

        // Clear previous timeout
        clearTimeout(cityTimeout);

        // Remove existing suggestions
        $('.city-suggestions').remove();

        if (query.length >= 2) {
          cityTimeout = setTimeout(function() {
            $.ajax({
              url: 'ajax/search_cities.php',
              type: 'POST',
              data: { query: query },
              success: function(data) {
                if (data && data.trim() !== '') {
                  const suggestions = $('<div class="city-suggestions"></div>');
                  suggestions.html(data);
                  $('#city-search').after(suggestions);

                  // Handle suggestion clicks
                  $('.city-suggestion').on('click', function() {
                    const cityName = $(this).text();
                    $('#city-search').val(cityName);
                    $('.city-suggestions').remove();
                  });
                }
              }
            });
          }, 300);
        }
      });

      // Hide suggestions when clicking outside
      $(document).on('click', function(e) {
        if (!$(e.target).closest('#city-search, .city-suggestions').length) {
          $('.city-suggestions').remove();
        }
      });
    });
  </script>

  <style>
    .city-suggestions {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border: 1px solid #ddd;
      border-radius: 10px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
      max-height: 200px;
      overflow-y: auto;
      z-index: 1000;
      margin-top: 5px;
    }

    .city-suggestion {
      padding: 12px 15px;
      cursor: pointer;
      border-bottom: 1px solid #f0f0f0;
      transition: background-color 0.2s;
    }

    .city-suggestion:hover {
      background-color: #f8f9fa;
    }

    .city-suggestion:last-child {
      border-bottom: none;
    }

    .search-form {
      position: relative;
    }
  </style>
</body>
</html>
