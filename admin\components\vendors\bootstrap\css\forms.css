/* FORMS */
input::-webkit-input-placeholder {
    color: #aca6cc !important;
}

input::-moz-placeholder {
    color: #aca6cc !important;
}

input:-moz-placeholder {
    color: #aca6cc !important;
}

input:-ms-input-placeholder {
    color: #aca6cc !important;
}

.form-actions {
    border-top: 1px solid #e4e9f0;
    padding-top: 1.33rem;
    margin: 1.33rem 0;
}

.form-control {
    font-family: "Source Sans Pro", sans-serif;
    border-color: #e4e9f0;
    color: #786fa4;
}

.form-control.form-control-rounded {
    border-radius: 500px;
}

.form-control:focus {
    border-color: #1b55e3;
}

.form-control.input-sm {
    padding: 0.26rem 0.53rem;
}

.col-form-label {
    padding-top: 0.6rem;
    padding-bottom: 0.6rem;
}

.form-control:disabled,
.form-control[readonly] {
    background-color: #f2f4f8;
}

.input-group-addon {
    border-color: #e4e9f0;
    background-color: #dde2ec;
}

span.input-group-addon {
    outline: none !important;
}

.form-group.has-danger .form-control {
    border-color: #fb434a;
}

.form-group.has-success .form-control {
    border-color: #46be8a;
}

.form-group.has-warning .form-control {
    border-color: #f39834;
}

.form-group.has-focused .form-control {
    border-color: #1b55e3;
}

.form-input-icon {
    position: relative;
}

.form-input-icon > i {
    color: #aca6cc;
    position: absolute;
    margin: 0.86rem 0.13rem 0.26rem 0.66rem;
    z-index: 3;
    width: 1.06rem;
    font-size: 1.06rem;
    text-align: center;
    left: 0;
}

.form-input-icon .form-control {
    padding-left: 2.26rem;
}

.form-input-icon.form-input-icon-right > i {
    left: auto;
    right: 0.2rem;
    margin: 0.86rem 0.66rem 0.26rem 0.13rem;
}

.form-input-icon.form-input-icon-right .form-control {
    padding-left: 1.06rem;
    padding-right: 2.26rem;
}

.input-group .add-on.input-group-addon {
    width: 30px;
    text-align: center;
    display: inline-block;
}

.input-group .add-on.input-group-addon i {
    line-height: 2.2;
}
