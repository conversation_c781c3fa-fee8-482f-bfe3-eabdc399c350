/* CARDS */
.card,
.card-header,
.card-footer {
    border-color: #e4e9f0;
    border-radius: calc(7px - 1px);
}

.card-header,
.card-footer,
.card-body {
    background: transparent;
    padding: 1.33rem 1.66rem;
}

@media (max-width: 991px) {
    .card-header,
    .card-footer,
    .card-body {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

.card-header:last-child,
.card-footer:last-child,
.card-body:last-child {
    border-radius: 0 0 calc(7px - 1px) calc(7px - 1px);
}

.card-header:first-child,
.card-footer:first-child,
.card-body:first-child {
    border-radius: calc(7px - 1px) calc(7px - 1px) 0 0;
}

.card {
    margin-bottom: 2rem;
    background: #fff;
}

.card-borderless {
    border-color: transparent !important;
}

.card-solid {
    color: #fff;
    border-color: transparent !important;
}

.card-solid h1,
.card-solid h2,
.card-solid h3,
.card-solid h4,
.card-solid h5,
.card-solid h6 {
    color: #fff;
}

.card-solid .card-header,
.card-solid .card-footer {
    border-color: rgba(255, 255, 255, 0.3);
}

.card-header-flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    padding-top: 0;
    padding-bottom: 0;
    min-height: 4.13rem;
}

.card-header-borderless {
    border-color: transparent !important;
}

.card-header-borderless + .card-body {
    padding-top: 0;
}
