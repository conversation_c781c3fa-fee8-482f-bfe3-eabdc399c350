<?php
session_start();
include('../admin/lib/db_connection.php');

date_default_timezone_set("Asia/Kolkata");
$date_time = date('Y-m-d H:i:s');

// Check if user is logged in and order exists
if(!isset($_SESSION['customer_id']) || !isset($_POST['order_id'])) {
    echo 0;
    exit;
}

$order_id = $_POST['order_id'];
$transaction_id = mysqli_real_escape_string($con, $_POST['transaction_id']);

// Validate order belongs to current user
$order_check = dbQuery("SELECT * FROM tabl_order WHERE id='".$order_id."' AND customer_id='".$_SESSION['customer_id']."'");
$order_exists = dbNumRows($order_check);

if($order_exists == 0) {
    echo 0;
    exit;
}

// Handle screenshot upload
$screenshot_name = '';
if(isset($_FILES['screenshot']) && $_FILES['screenshot']['error'] == 0) {
    $target_dir = "../assets/images/payment_screenshots/";
    
    // Create directory if it doesn't exist
    if (!file_exists($target_dir)) {
        mkdir($target_dir, 0777, true);
    }
    
    $file_extension = strtolower(pathinfo($_FILES['screenshot']['name'], PATHINFO_EXTENSION));
    $allowed_extensions = array('jpg', 'jpeg', 'png', 'gif');
    
    // Check file type
    if(!in_array($file_extension, $allowed_extensions)) {
        echo 2; // Invalid file type
        exit;
    }
    
    // Check file size (5MB max)
    if($_FILES['screenshot']['size'] > 5 * 1024 * 1024) {
        echo 3; // File too large
        exit;
    }
    
    $screenshot_name = 'payment_' . $order_id . '_' . time() . '.' . $file_extension;
    $target_file = $target_dir . $screenshot_name;
    
    if(!move_uploaded_file($_FILES['screenshot']['tmp_name'], $target_file)) {
        echo 4; // Upload error
        exit;
    }
} else {
    echo 0; // No file uploaded
    exit;
}

// Update order with transaction details
$update_order = dbQuery("UPDATE tabl_order SET 
                        transaction_id='".mysqli_real_escape_string($con, $transaction_id)."',
                        screenshot='".$screenshot_name."',
                        order_status_id='1'
                        WHERE id='".$order_id."'");

if($update_order) {
    // Clear cart after successful payment
    $session_id = session_id();
    dbQuery("DELETE FROM tabl_cart WHERE session_id='".$session_id."'");
    dbQuery("DELETE FROM tabl_cart_promo WHERE session_id='".$session_id."'");
    
    // Clear order from session
    unset($_SESSION['order_id']);
    unset($_SESSION['total_price']);
    
    echo 1; // Success
} else {
    echo 0; // Database error
}
?>
