<?php 
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page=1;
$sub_page=0;
date_default_timezone_set("Asia/Kolkata");
$date=date('Y-m-d');

if(isset($_REQUEST['submit'])){	
	$password=md5($_REQUEST['password']);	
	if($_FILES["profile_img"]["name"]!=""){		  
		  $target_dir = "components/core/img/avatars/";
		  $name = rand(10000,1000000);
		  $extension = pathinfo($_FILES["profile_img"]["name"], PATHINFO_EXTENSION);
		  $new_name=$name.".".$extension;
          $target_file = $target_dir . $name.".".$extension;

	$imageFileType = strtolower(pathinfo($target_file,PATHINFO_EXTENSION));	
if($imageFileType != "jpg" && $imageFileType != "png" && $imageFileType != "jpeg") {
    die("This is not valid image. Please try again.");
} else{  
	move_uploaded_file($_FILES["profile_img"]["tmp_name"], $target_file);
	 	 $target_path="components/core/img/avatars/".$new_name;
			$resizeObj = new resize("components/core/img/avatars/".$new_name);
			$resizeObj -> resizeImage(50, 50, 'crop');
			$resizeObj -> saveImage("components/core/img/avatars/thumb-50/".$new_name, 100);
        $upd=dbQuery("UPDATE tabl_delivery_admin SET user='".$_REQUEST['user']."',name='".$_REQUEST['name']."',profile_img='".$new_name."' WHERE id='".$_SESSION['user_id']."'");
   }
 } else{
	$upd=dbQuery("UPDATE tabl_delivery_admin SET user='".$_REQUEST['user']."',name='".$_REQUEST['name']."'  WHERE id='".$_SESSION['user_id']."'");
	
	 }
$_SESSION["user"] = $_REQUEST['user'];
$_SESSION["name"] = $_REQUEST['name']; 

echo '<script>alert("Profile Updated!");window.location.href="my-account.php"</script>';
	   
}
 ?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <title><?php echo SITE; ?> | My Account</title>
  <link href="favicon.png" rel="shortcut icon">
  <link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:400,400i,700,700i,900" rel="stylesheet">
  <!-- VENDORS -->
  <link rel="stylesheet" type="text/css" href="vendors/bootstrap/dist/css/bootstrap.css">
  <link rel="stylesheet" type="text/css" href="vendors/font-feathericons/dist/feather.css">
  <link rel="stylesheet" type="text/css" href="vendors/font-awesome/css/font-awesome.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/font-linearicons/style.css">
  <link rel="stylesheet" type="text/css" href="vendors/font-icomoon/style.css">
  <link rel="stylesheet" type="text/css" href="vendors/perfect-scrollbar/css/perfect-scrollbar.css">
  <link rel="stylesheet" type="text/css" href="vendors/chart.js/dist/Chart.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/jqvmap/dist/jqvmap.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/c3/c3.min.css">
  <link rel="stylesheet" type="text/css"
    href="../cdn.datatables.net/v/bs4/dt-1.10.18/fc-3.2.5/r-2.2.2/datatables.min.css" />
  <link rel="stylesheet" type="text/css"
    href="vendors/tempus-dominus-bs4/build/css/tempusdominus-bootstrap-4.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/fullcalendar/dist/fullcalendar.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/owl.carousel/dist/assets/owl.carousel.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/ionrangeslider/css/ion.rangeSlider.css">
  <link rel="stylesheet" type="text/css" href="vendors/bootstrap-sweetalert/dist/sweetalert.css">
  <link rel="stylesheet" type="text/css" href="vendors/nprogress/nprogress.css">
  <link rel="stylesheet" type="text/css" href="vendors/summernote/dist/summernote.css">
  <link rel="stylesheet" type="text/css" href="vendors/dropify/dist/css/dropify.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/jquery-steps/demo/css/jquery.steps.css">
  <link rel="stylesheet" type="text/css" href="vendors/select2/dist/css/select2.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/bootstrap-select/dist/css/bootstrap-select.min.css">


  <script src="vendors/jquery/dist/jquery.min.js"></script>
  <script src="vendors/popper.js/dist/umd/popper.js"></script>
  <script src="vendors/bootstrap/dist/js/bootstrap.js"></script>
  <script src="vendors/jquery-mousewheel/jquery.mousewheel.min.js"></script>
  <script src="vendors/perfect-scrollbar/js/perfect-scrollbar.jquery.js"></script>
  <script src="vendors/chartist/dist/chartist.min.js"></script>
  <script src="vendors/chart.js/dist/Chart.min.js"></script>
  <script src="vendors/jqvmap/dist/jquery.vmap.min.js"></script>
  <script src="vendors/jqvmap/dist/maps/jquery.vmap.usa.js"></script>
  <script src="vendors/chartist-plugin-tooltip/dist/chartist-plugin-tooltip.min.js"></script>
  <script src="vendors/d3/d3.min.js"></script>
  <script src="vendors/d3-dsv/dist/d3-dsv.js"></script>
  <script src="vendors/d3-time-format/dist/d3-time-format.js"></script>
  <script src="vendors/c3/c3.min.js"></script>
  <script src="vendors/peity/jquery.peity.min.js"></script>
  <script type="text/javascript"
    src="../cdn.datatables.net/v/bs4/dt-1.10.18/fc-3.2.5/r-2.2.2/datatables.min.js"></script>
  <script src="vendors/editable-table/mindmup-editabletable.js"></script>
  <script src="vendors/moment/min/moment.min.js"></script>
  <script src="vendors/tempus-dominus-bs4/build/js/tempusdominus-bootstrap-4.min.js"></script>
  <script src="vendors/fullcalendar/dist/fullcalendar.min.js"></script>
  <script src="vendors/owl.carousel/dist/owl.carousel.min.js"></script>
  <script src="vendors/ionrangeslider/js/ion.rangeSlider.min.js"></script>
  <script src="vendors/remarkable-bootstrap-notify/dist/bootstrap-notify.min.js"></script>
  <script src="vendors/bootstrap-sweetalert/dist/sweetalert.min.js"></script>
  <script src="vendors/nprogress/nprogress.js"></script>
  <script src="vendors/summernote/dist/summernote.min.js"></script>
  <script src="vendors/nestable/jquery.nestable.js"></script>
  <script src="vendors/jquery-typeahead/dist/jquery.typeahead.min.js"></script>
  <script src="vendors/autosize/dist/autosize.min.js"></script>
  <script src="vendors/bootstrap-show-password/dist/bootstrap-show-password.min.js"></script>
  <script src="vendors/dropify/dist/js/dropify.min.js"></script>
  <script src="vendors/html5-form-validation/dist/jquery.validation.min.js"></script>
  <script src="vendors/jquery-steps/build/jquery.steps.min.js"></script>
  <script src="vendors/jquery-mask-plugin/dist/jquery.mask.min.js"></script>
  <script src="vendors/select2/dist/js/select2.full.min.js"></script>
  <script src="vendors/bootstrap-select/dist/js/bootstrap-select.min.js"></script>
  <script src="vendors/d3-dsv/dist/d3-dsv.js"></script>
  <script src="vendors/d3-time-format/dist/d3-time-format.js"></script>
  <script src="vendors/techan/dist/techan.min.js"></script>
  <script src="vendors/Stickyfill/dist/stickyfill.min.js"></script>

  <!-- AIR UI HTML ADMIN TEMPLATE MODULES-->
  <link rel="stylesheet" type="text/css" href="components/vendors/style.css">
  <link rel="stylesheet" type="text/css" href="components/core/style.css">
  <link rel="stylesheet" type="text/css" href="components/widgets/style.css">
  <link rel="stylesheet" type="text/css" href="components/system/style.css">
  <link rel="stylesheet" type="text/css" href="components/menu-left/style.css">
  <link rel="stylesheet" type="text/css" href="components/menu-top/style.css">
  <link rel="stylesheet" type="text/css" href="components/footer/style.css">
  <link rel="stylesheet" type="text/css" href="components/footer-dark/style.css">
  <link rel="stylesheet" type="text/css" href="components/topbar/style.css">
  <link rel="stylesheet" type="text/css" href="components/topbar-dark/style.css">
  <link rel="stylesheet" type="text/css" href="components/subbar/style.css">
  <link rel="stylesheet" type="text/css" href="components/sidebar/style.css">
  <link rel="stylesheet" type="text/css" href="components/chat/style.css">
  <link rel="stylesheet" type="text/css" href="components/apps/style.css">
  <link rel="stylesheet" type="text/css" href="components/apps/style.css">
  <link rel="stylesheet" type="text/css" href="components/extra-apps/style.css">
  <link rel="stylesheet" type="text/css" href="components/ecommerce/style.css">
  <link rel="stylesheet" type="text/css" href="components/dashboards/crypto-terminal/style.css">

  <script src="components/core/index.js"></script>
  <script src="components/menu-left/index.js"></script>
  <script src="components/menu-top/index.js"></script>
  <script src="components/sidebar/index.js"></script>
  <script src="components/topbar/index.js"></script>
  <script src="components/chat/index.js"></script>
  <!-- PRELOADER STYLES-->
  
</head>
<body class="air__menu--blue air__menu__submenu--blue">
  <div class="air__initialLoading"></div>
  <div class="air__layout">
    <div class="air__menuTop">
      <div class="air__menuTop__outer">
        <div class="air__menuTop__mobileToggleButton air__menuTop__mobileActionToggle">
          <span></span>
        </div>
        <a href="home.php" class="air__menuTop__logo">
          <h1 style="color:#FFF"><?php echo SITE; ?></h1>
        </a>
        <?php include('inc/__menu.php'); ?>
      </div>
    </div>
 <div class="air__menuTop__backdrop air__menuTop__mobileActionToggle"></div>
   <div class="air__layout">
      <?php include('inc/__header.php');?>
      <div class="air__layout__content">
        <div class="air__utils__content">
          <div class="air__utils__heading">
  <h5>My Account: Profile</h5>
  <nav aria-label="breadcrumb" style="float: right;margin-top: -35px;">
          <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="home.php">Home</a></li>
            <li class="breadcrumb-item active" aria-current="page">Profile</li>
          </ol>
        </nav>
</div>
<?php 

$profile=dbQuery("SELECT * FROM tabl_delivery_admin WHERE id='".$_SESSION['user_id']."'");
    $res_profile=dbFetchAssoc($profile);
 ?>
<div class="row">
  <div class="col-xl-4 col-lg-12">
    <div class="card">
      <div class="card-body">
        <div class="d-flex flex-wrap flex-column align-items-center">
          <div class="air__utils__avatar air__utils__avatar--size64 mb-3">
            <img src="components/core/img/avatars/<?php echo $res_profile['profile_img'];?>" alt="<?php echo $res_profile['name'];?>" />
          </div>
          <div class="text-center">
            <div class="text-dark font-weight-bold font-size-18"><?php echo $res_profile['name'];?></div>
            <div class="text-uppercase font-size-12 mb-3">Super Admin</div>
          </div>
        </div>
      </div>
    </div>

  </div>
  <div class="col-xl-8 col-lg-12">
    <div class="card">
        <div class="card-body">
        <div class="tab-content"> 
          <div class="tab-pane fade show active" id="tab-settings-content" role="tabpanel" aria-labelledby="tab-settings-content">
            <h5 class="text-black mt-4">
              <strong>Personal Information</strong>
            </h5>
            <form name="account" method="post" enctype="multipart/form-data">
            <div class="row">
              <div class="col-lg-6">
                <div class="form-group">
                  <label class="form-control-label" for="l0">Username</label>
                  <input type="text" name="user" class="form-control" value="<?php echo $res_profile['user'];?>" required/>
                </div>
              </div>
              <div class="col-lg-6">
                <div class="form-group">
                  <label class="form-control-label" for="l1">Name</label>
                  <input type="text" name="name" class="form-control" value="<?php echo $res_profile['name'];?>" required/>
                </div>
              </div>
              <div class="col-lg-12">
                <div class="form-group">
                  <label class="form-control-label" for="l1">Profile Avatar</label>
                  <div class="form-group">
                  <label class="form-control-label" for="l6">File Upload</label>
                  <input type="file" name="profile_img" />
                </div>
                </div>
              </div>
            </div>
            <div class="form-actions">
              <div class="form-group">
                <button type="submit" name="submit" class="btn width-200 btn-primary">Submit</button>
                <button type="reset" class="btn btn-default">Cancel</button>
              </div>
            </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
        </div>
      </div>
      <?php include('inc/__footer.php'); ?>
    </div>
  </div>
</body>
</html>