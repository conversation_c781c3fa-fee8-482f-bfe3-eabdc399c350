/*  EONASDAN BOOTSTRAP DATEPICKER */
.bootstrap-datetimepicker-widget {
    max-width: 19em;
}

.bootstrap-datetimepicker-widget table thead tr:first-child th:hover {
    background: none;
    color: #1b55e3 !important;
}

.bootstrap-datetimepicker-widget table thead tr:first-child th.prev, .bootstrap-datetimepicker-widget table thead tr:first-child th.next {
    color: #c3bedc;
}

.bootstrap-datetimepicker-widget table td {
    -webkit-transition: all 0.1s ease-in-out;
    transition: all 0.1s ease-in-out;
}

.bootstrap-datetimepicker-widget table td span.active {
    background: #1b55e3 !important;
}

.bootstrap-datetimepicker-widget table td span:hover {
    background-color: #786fa4;
    color: #fff;
}

.bootstrap-datetimepicker-widget table td span.timepicker-hour, .bootstrap-datetimepicker-widget table td span.timepicker-minute {
    color: #161537;
    background: none !important;
}

.bootstrap-datetimepicker-widget table td.minute:hover {
    background-color: #f2f4f8;
}

.bootstrap-datetimepicker-widget table th {
    width: 32px;
    height: 28px;
    line-height: 28px;
}

.bootstrap-datetimepicker-widget table th.dow {
    font-weight: 600;
}

.bootstrap-datetimepicker-widget table th.picker-switch {
    width: 165px;
}

.bootstrap-datetimepicker-widget table td.day {
    width: 32px;
    height: 28px;
    line-height: 28px;
}

.bootstrap-datetimepicker-widget table td.day:hover {
    background: #786fa4;
    color: #fff;
}

.bootstrap-datetimepicker-widget table td.day.new {
    color: #e4e9f0;
}

.bootstrap-datetimepicker-widget table td.day.new:hover {
    color: #fff !important;
}

.bootstrap-datetimepicker-widget table td.day.old {
    color: #e4e9f0;
}

.bootstrap-datetimepicker-widget table td.day.old:hover {
    color: #fff !important;
}

.bootstrap-datetimepicker-widget table td.active.active, .bootstrap-datetimepicker-widget table td.active:active:hover,
.bootstrap-datetimepicker-widget table table td.active:hover.active,
.bootstrap-datetimepicker-widget table table td.active:hover:active:hover {
    background-color: #1b55e3;
    color: #fff !important;
}

.bootstrap-datetimepicker-widget .timepicker-hours td:hover,
.bootstrap-datetimepicker-widget .timepicker-minutes td:hover {
    background: #786fa4 !important;
    color: #fff;
}

.bootstrap-datetimepicker-widget a[data-action].btn {
    padding: 0;
    background: none;
    color: #161537;
    border: none !important;
}

.bootstrap-datetimepicker-widget a[data-action].btn:hover {
    color: #fff;
    background: #786fa4;
}

.bootstrap-datetimepicker-widget a[data-action].btn:hover span {
    background: none !important;
}

.bootstrap-datetimepicker-widget .accordion-toggle span {
    color: #161537;
    background: #e4e9f0;
}

.bootstrap-datetimepicker-widget .accordion-toggle span:hover {
    color: #fff;
    background-color: #1b55e3;
}

.bootstrap-datetimepicker-widget.dropdown-menu {
    width: 300px;
}

.bootstrap-datetimepicker-widget.dropdown-menu:before, .bootstrap-datetimepicker-widget.dropdown-menu:after {
    display: none;
}
