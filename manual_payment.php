<?php
session_start();
include('admin/lib/db_connection.php');
include('customer_auth.php');

// Check if order exists in session
if (!isset($_SESSION['order_id'])) {
  echo '<script>window.location.href="cart.php"</script>';
  exit;
}

$order_id = $_SESSION['order_id'];
$total_price = $_SESSION['total_price'];

// Get payment settings
$settings = dbQuery("SELECT * FROM tabl_delivery_setting WHERE id='1'");
$res_settings = dbFetchAssoc($settings);

// Get order details
$order = dbQuery("SELECT * FROM tabl_order WHERE id='" . $order_id . "'");
$res_order = dbFetchAssoc($order);

date_default_timezone_set('Asia/Kolkata');
$date_time = date('Y-m-d H:i:s');
?>
<!DOCTYPE html>
<html lang="en">

<head>
  <title>Manual Payment - Project Womaniya</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, minimal-ui, viewport-fit=cover">
  <meta name="theme-color" content="#009688">
  <link rel="shortcut icon" type="image/x-icon" href="assets/images/favicon.png">
  <link rel="stylesheet" type="text/css" href="assets/css/style.css">
  <link rel="preconnect" href="https://fonts.googleapis.com/">
  <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&amp;family=Poppins:wght@200;300;400;500;600;700;800;900&amp;display=swap" rel="stylesheet">

  <style>
    .payment-container {
      padding: 20px;
      background: #f8f9fa;
      min-height: 100vh;
    }

    .payment-card {
      background: white;
      border-radius: 15px;
      padding: 25px;
      margin-bottom: 20px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .qr-code-section {
      text-align: center;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 15px;
      color: white;
      margin-bottom: 20px;
    }

    .qr-code-image {
      background: white;
      padding: 20px;
      border-radius: 15px;
      display: inline-block;
      margin: 15px 0;
    }

    .qr-code-image img {
      max-width: 250px;
      width: 100%;
      height: auto;
    }

    .upi-info {
      background: #e3f2fd;
      padding: 15px;
      border-radius: 10px;
      margin: 15px 0;
      border-left: 4px solid #2196f3;
    }

    .order-summary {
      background: #f1f8e9;
      padding: 15px;
      border-radius: 10px;
      border-left: 4px solid #4caf50;
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-label {
      font-weight: 600;
      margin-bottom: 8px;
      display: block;
      color: #333;
    }

    .form-control {
      width: 100%;
      padding: 12px 15px;
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      font-size: 16px;
      transition: border-color 0.3s;
    }

    .form-control:focus {
      outline: none;
      border-color: #007bff;
    }

    .file-upload {
      position: relative;
      display: inline-block;
      width: 100%;
    }

    .file-upload input[type=file] {
      position: absolute;
      opacity: 0;
      width: 100%;
      height: 100%;
      cursor: pointer;
    }

    .file-upload-label {
      display: block;
      padding: 12px 15px;
      border: 2px dashed #007bff;
      border-radius: 8px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s;
    }

    .file-upload-label:hover {
      background: #f8f9fa;
      border-color: #0056b3;
    }

    .preview-image {
      max-width: 200px;
      margin-top: 10px;
      border-radius: 8px;
      border: 2px solid #ddd;
    }

    .btn-submit {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: 15px 30px;
      border-radius: 25px;
      font-size: 16px;
      font-weight: 600;
      width: 100%;
      cursor: pointer;
      transition: transform 0.2s;
    }

    .btn-submit:hover {
      transform: translateY(-2px);
    }

    .btn-submit:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    .instructions {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 10px;
      padding: 15px;
      margin-bottom: 20px;
    }

    .step {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }

    .step-number {
      background: #007bff;
      color: white;
      width: 25px;
      height: 25px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      margin-right: 10px;
      font-size: 12px;
    }
  </style>
</head>

<body>
  <div class="page-wraper">
    <!-- Preloader -->
    <div id="preloader">
      <div class="loader">
        <div class="load-circle">
          <div></div>
          <div></div>
        </div>
      </div>
    </div>

    <!-- Header -->
    <header class="header">
      <div class="main-bar">
        <div class="container">
          <div class="header-content">
            <div class="left-content">
              <a href="payment.php" class="back-btn">
                <i class="fa-solid fa-arrow-left"></i>
              </a>
              <h4 class="title mb-0 text-nowrap">Manual Payment</h4>
            </div>
            <div class="mid-content"></div>
            <div class="right-content"></div>
          </div>
        </div>
      </div>
    </header>

    <!-- Page Content -->
    <div class="page-content payment-container">
      <div class="container">

        <!-- Order Summary -->
        <div class="payment-card order-summary">
          <h5><i class="fa-solid fa-receipt me-2"></i>Order Summary</h5>
          <p><strong>Order ID:</strong> #<?php echo $order_id; ?></p>
          <p><strong>Total Amount:</strong> ₹<?php echo number_format($total_price, 2); ?></p>
        </div>

        <!-- Payment Instructions -->
        <div class="payment-card instructions">
          <h5><i class="fa-solid fa-info-circle me-2"></i>Payment Instructions</h5>
          <div class="step">
            <div class="step-number">1</div>
            <span>Scan the QR code or use the UPI ID below to make payment</span>
          </div>
          <div class="step">
            <div class="step-number">2</div>
            <span>Enter the transaction ID from your payment app</span>
          </div>
          <div class="step">
            <div class="step-number">3</div>
            <span>Upload a screenshot of the payment confirmation</span>
          </div>
          <div class="step">
            <div class="step-number">4</div>
            <span>Submit to complete your order</span>
          </div>
        </div>

        <!-- QR Code Section -->
        <?php if (!empty($res_settings['qr_code']) || !empty($res_settings['upi_id'])): ?>
          <div class="payment-card">
            <div class="qr-code-section">
              <h5><i class="fa-solid fa-qrcode me-2"></i>Scan & Pay</h5>
              <p>Amount: ₹<?php echo number_format($total_price, 2); ?></p>

              <?php if (!empty($res_settings['qr_code'])): ?>
                <div class="qr-code-image">
                  <img src="assets/images/qr_code/<?php echo $res_settings['qr_code']; ?>" alt="Payment QR Code">
                </div>
              <?php endif; ?>
            </div>

            <?php if (!empty($res_settings['upi_id'])): ?>
              <div class="upi-info">
                <h6><i class="fa-solid fa-mobile-alt me-2"></i>UPI ID</h6>
                <p style="font-family: monospace; font-size: 18px; font-weight: bold; color: #1976d2;">
                  <?php echo $res_settings['upi_id']; ?>
                </p>
                <small>Copy this UPI ID to make payment from any UPI app</small>
              </div>
            <?php endif; ?>
          </div>
        <?php else: ?>
          <div class="payment-card">
            <div class="alert alert-warning">
              <h6>Payment Information Not Available</h6>
              <p>Please contact support for payment details.</p>
            </div>
          </div>
        <?php endif; ?>

        <!-- Payment Confirmation Form -->
        <div class="payment-card">
          <h5><i class="fa-solid fa-check-circle me-2"></i>Payment Confirmation</h5>

          <form id="manual_payment_form" enctype="multipart/form-data">
            <input type="hidden" name="order_id" value="<?php echo $order_id; ?>">

            <div class="form-group">
              <label class="form-label">Transaction ID *</label>
              <input type="text" name="transaction_id" class="form-control" placeholder="Enter transaction ID from your payment app" required>
              <small class="text-muted">Enter the transaction ID/reference number from your UPI payment</small>
            </div>

            <div class="form-group">
              <label class="form-label">Payment Screenshot *</label>
              <div class="file-upload">
                <input type="file" name="screenshot" id="screenshot" accept="image/*" required>
                <label for="screenshot" class="file-upload-label">
                  <i class="fa-solid fa-cloud-upload-alt me-2"></i>
                  Click to upload payment screenshot
                </label>
              </div>
              <div id="image-preview"></div>
              <small class="text-muted">Upload a clear screenshot of your payment confirmation</small>
            </div>

            <div class="payment_msg"></div>

            <button type="submit" id="payment_submit" class="btn-submit">
              <i class="fa-solid fa-check me-2"></i>Confirm Payment
            </button>
          </form>
        </div>

      </div>
    </div>
  </div>

  <script src="assets/js/jquery.js"></script>
  <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="assets/js/settings.js"></script>
  <script src="assets/js/custom.js"></script>

  <script>
    // Image preview functionality
    document.getElementById('screenshot').addEventListener('change', function(e) {
      const file = e.target.files[0];
      const preview = document.getElementById('image-preview');

      if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
          preview.innerHTML = `
            <div style="margin-top: 10px;">
              <p><strong>Preview:</strong></p>
              <img src="${e.target.result}" class="preview-image" alt="Screenshot Preview">
            </div>
          `;
        };
        reader.readAsDataURL(file);

        // Update label text
        document.querySelector('.file-upload-label').innerHTML = `
          <i class="fa-solid fa-check me-2"></i>
          ${file.name} selected
        `;
      } else {
        preview.innerHTML = '';
        document.querySelector('.file-upload-label').innerHTML = `
          <i class="fa-solid fa-cloud-upload-alt me-2"></i>
          Click to upload payment screenshot
        `;
      }
    });

    // Form submission
    $("#manual_payment_form").submit(function(e) {
      e.preventDefault();

      $("#payment_submit").attr('disabled', true);
      $("#payment_submit").html('<i class="spinner-border spinner-border-sm me-2"></i>Processing...');

      // Create FormData object for file upload
      var formData = new FormData(this);

      $.ajax({
        url: 'ajax/process_manual_payment.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(data) {
          if (data == 1) {
            window.location.href = 'order-thanks.php';
          } else if (data == 2) {
            $("#payment_submit").attr('disabled', false);
            $("#payment_submit").html('<i class="fa-solid fa-check me-2"></i>Confirm Payment');
            $(".payment_msg").html('<div class="alert alert-danger">Invalid file type. Please upload JPG, PNG, or GIF image.</div>');
          } else if (data == 3) {
            $("#payment_submit").attr('disabled', false);
            $("#payment_submit").html('<i class="fa-solid fa-check me-2"></i>Confirm Payment');
            $(".payment_msg").html('<div class="alert alert-danger">File size too large. Please upload image less than 5MB.</div>');
          } else if (data == 4) {
            $("#payment_submit").attr('disabled', false);
            $("#payment_submit").html('<i class="fa-solid fa-check me-2"></i>Confirm Payment');
            $(".payment_msg").html('<div class="alert alert-danger">Error uploading file. Please try again.</div>');
          } else {
            $("#payment_submit").attr('disabled', false);
            $("#payment_submit").html('<i class="fa-solid fa-check me-2"></i>Confirm Payment');
            $(".payment_msg").html('<div class="alert alert-danger">Something went wrong. Please try again.</div>');
          }
        },
        error: function() {
          $("#payment_submit").attr('disabled', false);
          $("#payment_submit").html('<i class="fa-solid fa-check me-2"></i>Confirm Payment');
          $(".payment_msg").html('<div class="alert alert-danger">Network error. Please check your connection and try again.</div>');
        }
      });
    });

    // Copy UPI ID functionality
    function copyUpiId() {
      const upiId = "<?php echo $res_settings['upi_id']; ?>";
      navigator.clipboard.writeText(upiId).then(function() {
        alert('UPI ID copied to clipboard!');
      });
    }
  </script>
</body>

</html>