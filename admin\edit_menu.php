<?php 
session_start();
include('../admin/lib/db_connection.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page=99;
$sub_page=0;
date_default_timezone_set("Asia/Kolkata");
$date=date('Y-m-d H:i:s');

if(isset($_REQUEST['submit'])){

if($_FILES["cover_image"]["name"]!=""){
		  $target_dir = "../assets/products/";

		  // Create directory if it doesn't exist
		  if (!file_exists($target_dir)) {
		      mkdir($target_dir, 0777, true);
		  }
		  if (!file_exists($target_dir . "thumb-100/")) {
		      mkdir($target_dir . "thumb-100/", 0777, true);
		  }
		  if (!file_exists($target_dir . "thumb-600/")) {
		      mkdir($target_dir . "thumb-600/", 0777, true);
		  }

		  $name = rand(10000,1000000);
		  $extension = pathinfo($_FILES["cover_image"]["name"], PATHINFO_EXTENSION);
		  $new_name=$name.".".$extension;
          $target_file = $target_dir . $name.".".$extension;

	$imageFileType = strtolower(pathinfo($target_file,PATHINFO_EXTENSION));	
if($imageFileType != "jpg" && $imageFileType != "png" && $imageFileType != "jpeg") {
    die("This is not valid image. Please try again.");
} else{  
	move_uploaded_file($_FILES["cover_image"]["tmp_name"], $target_file);
	 	 $target_path="../assets/products/".$new_name;
			$resizeObj = new resize("../assets/products/".$new_name);
			$resizeObj -> resizeImage(100, 100, 'exact');
			$resizeObj -> saveImage("../assets/products/thumb-100/".$new_name, 100);
			
			$resizeObj = new resize("../assets/products/".$new_name);
			$resizeObj -> resizeImage(600, 600, 'exact');
			$resizeObj -> saveImage("../assets/products/thumb-600/".$new_name, 100);

       	 
		dbQuery("UPDATE tabl_products SET name='".mysqli_real_escape_string($con,$_REQUEST['name'])."',category_id='".$_REQUEST['category_id']."',sub_category_id='".$_REQUEST['sub_category_id']."',food_type='".$_REQUEST['food_type']."',price='".mysqli_real_escape_string($con,$_REQUEST['price'])."',description='".mysqli_real_escape_string($con,$_REQUEST['description'])."',image='".$new_name."',status='".$_REQUEST['status']."' WHERE id='".$_REQUEST['id']."'");
  }
}else{
		dbQuery("UPDATE tabl_products SET name='".mysqli_real_escape_string($con,$_REQUEST['name'])."',category_id='".$_REQUEST['category_id']."',sub_category_id='".$_REQUEST['sub_category_id']."',food_type='".$_REQUEST['food_type']."',price='".mysqli_real_escape_string($con,$_REQUEST['price'])."',description='".mysqli_real_escape_string($con,$_REQUEST['description'])."',status='".$_REQUEST['status']."' WHERE id='".$_REQUEST['id']."'");
}

$num=$_REQUEST['num']-1;
for($i=1;$i<=$num;$i++){
      		  dbQuery("insert into tabl_product_type set 										
										p_id='".$_REQUEST['id']."',
										p_name='".mysqli_real_escape_string($con,$_REQUEST['p_name_'.$i])."',								
										price='".$_REQUEST['price_'.$i]."'"); 
  }

 echo '<script>alert("Products Update Successfully!");window.location.href="menu.php"</script>';
}

$sel=dbQuery("SELECT * FROM tabl_products WHERE id='".$_REQUEST['id']."'");
$res=dbFetchAssoc($sel);
 ?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <title><?php echo SITE; ?> | Edit Menu</title>
  <link href="favicon.png" rel="shortcut icon">
  <link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:400,400i,700,700i,900" rel="stylesheet">
  <!-- VENDORS -->
  <link rel="stylesheet" type="text/css" href="vendors/bootstrap/dist/css/bootstrap.css">
  <link rel="stylesheet" type="text/css" href="vendors/font-feathericons/dist/feather.css">
  <link rel="stylesheet" type="text/css" href="vendors/font-awesome/css/font-awesome.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/font-linearicons/style.css">
  <link rel="stylesheet" type="text/css" href="vendors/font-icomoon/style.css">
  <link rel="stylesheet" type="text/css" href="vendors/perfect-scrollbar/css/perfect-scrollbar.css">
  <link rel="stylesheet" type="text/css" href="vendors/chart.js/dist/Chart.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/jqvmap/dist/jqvmap.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/c3/c3.min.css">
  <link rel="stylesheet" type="text/css"
    href="cdn.datatables.net/v/bs4/dt-1.10.18/fc-3.2.5/r-2.2.2/datatables.min.css" />
  <link rel="stylesheet" type="text/css" href="vendors/tempus-dominus-bs4/build/css/tempusdominus-bootstrap-4.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/fullcalendar/dist/fullcalendar.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/owl.carousel/dist/assets/owl.carousel.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/ionrangeslider/css/ion.rangeSlider.css">
  <link rel="stylesheet" type="text/css" href="vendors/bootstrap-sweetalert/dist/sweetalert.css">
  <link rel="stylesheet" type="text/css" href="vendors/nprogress/nprogress.css">
  <link rel="stylesheet" type="text/css" href="vendors/summernote/dist/summernote.css">
  <link rel="stylesheet" type="text/css" href="vendors/dropify/dist/css/dropify.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/jquery-steps/demo/css/jquery.steps.css">
  <link rel="stylesheet" type="text/css" href="vendors/select2/dist/css/select2.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/bootstrap-select/dist/css/bootstrap-select.min.css">


  <script src="vendors/jquery/dist/jquery.min.js"></script>
  <script src="vendors/popper.js/dist/umd/popper.js"></script>
  <script src="vendors/bootstrap/dist/js/bootstrap.js"></script>
  <script src="vendors/jquery-mousewheel/jquery.mousewheel.min.js"></script>
  <script src="vendors/perfect-scrollbar/js/perfect-scrollbar.jquery.js"></script>
  <script src="vendors/chartist/dist/chartist.min.js"></script>
  <script src="vendors/chart.js/dist/Chart.min.js"></script>
  <script src="vendors/jqvmap/dist/jquery.vmap.min.js"></script>
  <script src="vendors/jqvmap/dist/maps/jquery.vmap.usa.js"></script>
  <script src="vendors/chartist-plugin-tooltip/dist/chartist-plugin-tooltip.min.js"></script>
  <script src="vendors/d3/d3.min.js"></script>
  <script src="vendors/d3-dsv/dist/d3-dsv.js"></script>
  <script src="vendors/d3-time-format/dist/d3-time-format.js"></script>
  <script src="vendors/c3/c3.min.js"></script>
  <script src="vendors/peity/jquery.peity.min.js"></script>
  <script type="text/javascript" src="cdn.datatables.net/v/bs4/dt-1.10.18/fc-3.2.5/r-2.2.2/datatables.min.js"></script>
  <script src="vendors/editable-table/mindmup-editabletable.js"></script>
  <script src="vendors/moment/min/moment.min.js"></script>
  <script src="vendors/tempus-dominus-bs4/build/js/tempusdominus-bootstrap-4.min.js"></script>
  <script src="vendors/fullcalendar/dist/fullcalendar.min.js"></script>
  <script src="vendors/owl.carousel/dist/owl.carousel.min.js"></script>
  <script src="vendors/ionrangeslider/js/ion.rangeSlider.min.js"></script>
  <script src="vendors/remarkable-bootstrap-notify/dist/bootstrap-notify.min.js"></script>
  <script src="vendors/bootstrap-sweetalert/dist/sweetalert.min.js"></script>
  <script src="vendors/nprogress/nprogress.js"></script>
  <script src="vendors/summernote/dist/summernote.min.js"></script>
  <script src="vendors/nestable/jquery.nestable.js"></script>
  <script src="vendors/jquery-typeahead/dist/jquery.typeahead.min.js"></script>
  <script src="vendors/autosize/dist/autosize.min.js"></script>
  <script src="vendors/bootstrap-show-password/dist/bootstrap-show-password.min.js"></script>
  <script src="vendors/dropify/dist/js/dropify.min.js"></script>
  <script src="vendors/html5-form-validation/dist/jquery.validation.min.js"></script>
  <script src="vendors/jquery-steps/build/jquery.steps.min.js"></script>
  <script src="vendors/jquery-mask-plugin/dist/jquery.mask.min.js"></script>
  <script src="vendors/select2/dist/js/select2.full.min.js"></script>
  <script src="vendors/bootstrap-select/dist/js/bootstrap-select.min.js"></script>
  <script src="vendors/d3-dsv/dist/d3-dsv.js"></script>
  <script src="vendors/d3-time-format/dist/d3-time-format.js"></script>
  <script src="vendors/techan/dist/techan.min.js"></script>
  <script src="vendors/Stickyfill/dist/stickyfill.min.js"></script>
  <!-- <script src="https://cdn.ckeditor.com/4.16.0/standard/ckeditor.js"></script> -->

  <!-- Summernote CSS -->
  <link rel="stylesheet" href="assets/summernote/css/summernote.min.css">


  <!-- Summernote lite javascript -->
  <script src="assets/summernote/js/summernote-lite.min.js"></script>

  <!-- AIR UI HTML ADMIN TEMPLATE MODULES-->
  <link rel="stylesheet" type="text/css" href="components/vendors/style.css">
  <link rel="stylesheet" type="text/css" href="components/core/style.css">
  <link rel="stylesheet" type="text/css" href="components/widgets/style.css">
  <link rel="stylesheet" type="text/css" href="components/system/style.css">
  <link rel="stylesheet" type="text/css" href="components/menu-left/style.css">
  <link rel="stylesheet" type="text/css" href="components/menu-top/style.css">
  <link rel="stylesheet" type="text/css" href="components/footer/style.css">
  <link rel="stylesheet" type="text/css" href="components/footer-dark/style.css">
  <link rel="stylesheet" type="text/css" href="components/topbar/style.css">
  <link rel="stylesheet" type="text/css" href="components/topbar-dark/style.css">
  <link rel="stylesheet" type="text/css" href="components/subbar/style.css">
  <link rel="stylesheet" type="text/css" href="components/sidebar/style.css">
  <link rel="stylesheet" type="text/css" href="components/chat/style.css">
  <link rel="stylesheet" type="text/css" href="components/apps/style.css">
  <link rel="stylesheet" type="text/css" href="components/apps/style.css">
  <link rel="stylesheet" type="text/css" href="components/extra-apps/style.css">
  <link rel="stylesheet" type="text/css" href="components/ecommerce/style.css">
  <link rel="stylesheet" type="text/css" href="components/dashboards/crypto-terminal/style.css">

  <script src="components/core/index.js"></script>
  <script src="components/menu-left/index.js"></script>
  <script src="components/menu-top/index.js"></script>
  <script src="components/sidebar/index.js"></script>
  <script src="components/topbar/index.js"></script>
  <script src="components/chat/index.js"></script>

  <!-- PRELOADER STYLES-->
  
</head>
<body class="air__menu--blue air__menu__submenu--blue">
  <div class="air__initialLoading"></div>
  <div class="air__layout">
    <div class="air__menuTop">
      <div class="air__menuTop__outer">
        <div class="air__menuTop__mobileToggleButton air__menuTop__mobileActionToggle">
          <span></span>
        </div>
        <a href="home.php" class="air__menuTop__logo">
          <h1 style="color:#FFF"><?php echo SITE; ?></h1>
        </a>
        <?php include('inc/__menu.php'); ?>
      </div>
    </div>
 <div class="air__menuTop__backdrop air__menuTop__mobileActionToggle"></div>
   <div class="air__layout">
      <?php include('inc/__header.php');?>
      <div class="air__layout__content">
        <div class="air__utils__content">
          <div class="air__utils__heading">
  <h5>Menu: Edit Menu</h5>
  <nav aria-label="breadcrumb" style="float: right;margin-top: -35px;">
          <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="home.php">Home</a></li>
            <li class="breadcrumb-item active" aria-current="page">Edit Menu</li>
          </ol>
        </nav>
</div>
<div class="row">
  <div class="col-lg-12">
    <div class="card">
      <div class="card-body" style="background: aliceblue;">
       <form method="POST" enctype="multipart/form-data"> 
     
        <div class="row">
        
        <div class="col-lg-6">
               <div class="form-group">
                <label class="form-label">Name</label>
                <input class="form-control"  name="name" id="name" value="<?php echo $res['name'];?>" type="text" required/>
               </div> 
             </div>
        
             <div class="col-lg-3">
               <div class="form-group">
                <label class="form-label">Category</label>
                <select name="category_id" class="form-control" onChange="get_sub_category(this.value)" required>
				   <option value="">SELECT</option>
				   <?php $cat=dbQuery("SELECT * FROM tabl_category ORDER BY id ASC");
				         while($res_cat=dbFetchAssoc($cat)){
							  if($res_cat['id']==$res['category_id']){
								  $selected='selected';
								  }else{
									  $selected='';
									  }
							   echo ' <option value="'.$res_cat['id'].'" '.$selected.'>'.$res_cat['category'].'</option>';
							 }
							 ?>
                
                </select>
              
               </div> 
             </div>
             
             <div class="col-lg-3">
               <div class="form-group">
                <label class="form-label">Sub-Category</label>
                <select name="sub_category_id" id="sub_category_id"  class="form-control" required>
				   <option value="">SELECT</option>
	   <?php $subcat=dbQuery("SELECT * FROM tabl_sub_category WHERE category_id='".$res['category_id']."' ORDER BY id ASC");
				         while($res_subcat=dbFetchAssoc($subcat)){
							  if($res_subcat['id']==$res['sub_category_id']){
								  $selected='selected';
								  }else{
									  $selected='';
									  }
							   echo ' <option value="'.$res_subcat['id'].'" '.$selected.'>'.$res_subcat['sub_category'].'</option>';
							 }
							 ?>
                
                </select>
              
               </div> 
             </div>
           
            
             <div class="col-lg-6">
               <div class="form-group">
                <label class="form-label">Food Type</label>
                <select name="food_type" class="form-control" required>
				   <option value="">SELECT</option>
                   <option value="1" <?php if($res['food_type']==1){ echo 'selected'; }else{ echo '';}?>>Veg</option>
                   <option value="2" <?php if($res['food_type']==2){ echo 'selected'; }else{ echo '';}?>>Non-Veg</option>
				   
                
                </select>
               </div> 
             </div>
             
             
             <div class="col-lg-4">
               <div class="form-group">
                <label class="form-label">Price</label>
                 <input class="form-control"  name="price" id="price" value="<?php echo $res['price'];?>" onkeypress="return isDecimal(event,this)" type="text" required/>
               </div> 
             </div>
             
             <div class="col-lg-2">
               <div class="form-group">
                <label class="form-label">Status</label>
                 <select name="status" class="form-control">
                     <option value="">SELECT</option>
                     <option value="1" <?php if($res['status']==1){echo 'selected';}else{echo '';} ?>>Active</option>
                     <option value="0" <?php if($res['status']==0){echo 'selected';}else{echo '';} ?>>Disabled</option>
                   
                 </select>
                
               </div> 
             </div>
             
             
              <div class="col-lg-2">
               <div class="form-group">
                <label class="form-label" for="validation-password"></label>
                 <img alt="avatar" class="img-fluid" src="../assets/products/thumb-100/<?php echo $res['image']; ?>" width="80">
              </div>
              </div>
              
              
              <div class="col-lg-10">
               <div class="form-group">
                <label class="form-label" for="validation-password">Update Image</label>
                  <input class="form-control" name="cover_image" type="file"/>
              </div>
           </div>
           
             
          
             <div class="col-lg-12">
               <div class="form-group">
                <label class="form-label" for="validation-password">Description</label>
                 <textarea name="description" id="description" class="form-control" required/><?php echo $res['description'];?></textarea>
              </div>
               
                <div class="col-xl-12 col-md-12 col-sm-12 col-12">
		<h5 style="color:#d06a6a">Add Plans</h5>
	</div>
    
		        <table class="table table-bordered table-striped">
		    <tr>
			    <th>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus-square" onClick="displayResult()" name="add" style="cursor:pointer;"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg>
            </th>
			   <th>S.No.</th>
			   <th>Name</th>
               <th>Price</th>
			</tr>
		 <tbody id="images">
          </tbody>
		</table>
        
         <table class="table table-bordered table-striped">
		    <tr>
			   <th>S.No.</th>
			   <th>Name</th>
               <th>Price</th>
			   <th>Delete</th>
			</tr>
		<?php 
		$sel_details=dbQuery("SELECT * FROM `tabl_product_type` WHERE p_id='".$res['id']."'");
		          $i=1;
				  while($res_sel_details=dbFetchAssoc($sel_details)){?> 
		     <tr class="image_rows_<?php echo $i;?>">
			    <td><?php echo $i; ?></td>
				<td><?php echo $res_sel_details['p_name']; ?></td>
                <td><?php echo $res_sel_details['price']; ?></td>
				 <td><a href="javascript:void(0)" onClick="delete_product_image(<?php echo $res_sel_details['id'] ?>,<?php echo $i?>)"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-trash-2"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg></a></td>  
			 </tr>
				  <?php $i++; } ?> 
		</table>
        	
        
          <div class="form-actions">
               <input type="hidden" name="num" id="num" value="1"/>
               <button type="submit" name="submit" class="btn btn-success mr-2 px-5">Submit</button>
              </div>
             
             </div>  
              
                  
        </div>
   </form>
      </div>
    </div>
  </div>
</div>
        </div>
      </div>
      <?php include('inc/__footer.php'); ?>
    </div>
  </div>
</body>
</html>
<script>
  ;(function($) {
    'use strict'
    $(function() {
      $('#example1').DataTable({
        responsive: true,
      })

      $('#example2').DataTable({
        autoWidth: true,
        scrollX: true,
        fixedColumns: true,
      })

      $('#example3').DataTable({
        autoWidth: true,
        scrollX: true,

        fixedColumns: true,
      })
    })
  })(jQuery)
</script>
<script>
  ;(function($) {
    'use strict'
    $(function() {
      $('#form-validation').validate({
        submit: {
          settings: {
            inputContainer: '.form-group',
            errorListClass: 'form-control-error',
            errorClass: 'has-danger',
          },
        },
      })

      $('#form-validation .remove-error').on('click', function() {
        $('#form-validation').removeError()
      })

      $('#form-validation-simple').validate({
        submit: {
          settings: {
            inputContainer: '.form-group',
            errorListClass: 'form-control-error-list',
            errorClass: 'has-danger',
          },
        },
      })

      $('#form-validation-simple .remove-error').on('click', function() {
        $('#form-validation-simple').removeError()
      })

      $('.select2').select2()
    })
  })(jQuery)
</script>
<script>

  function displayResult()

        {
        
	   var i=document.getElementById("num").value;  

	   	document.getElementById("images").insertRow(-1).innerHTML = '<tr><td> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus-square" onClick="displayResult()" name="add" style="cursor:pointer;"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg></td><td>'+i+'</td><td><input type="text" name="p_name_'+i+'" id="" class="form-control" ></td><td><input type="text" name="price_'+i+'" onkeypress="return isDecimal(event,this)" class="form-control"></td></tr>';
       i++;

     var num=document.getElementById("num").value = i;

		 }

</script>



<script>
   function isNumber(evt) {
        var iKeyCode = (evt.which) ? evt.which : evt.keyCode
        if (iKeyCode != 46 && iKeyCode > 31 && (iKeyCode < 48 || iKeyCode > 57))
            return false;

        return true;
    }
	function isDecimal(evt, obj) {

            var charCode = (evt.which) ? evt.which : event.keyCode
            var value = obj.value;
            var dotcontains = value.indexOf(".") != -1;
            if (dotcontains)
                if (charCode == 46) return false;
            if (charCode == 46) return true;
            if (charCode > 31 && (charCode < 48 || charCode > 57))
                return false;
            return true;
        }

</script>
<script>
                // Replace the <textarea id="editor1"> with a CKEditor 4
                // instance, using default configuration.
            // CKEDITOR.replace('specification');

    $('#specification').summernote({
      placeholder: 'Enter Content Here...',
      tabsize: 2,
      height: 200,
      toolbar: [
        ['style', ['style']],
        ['font', ['bold', 'underline', 'clear']],
        ['color', ['color']],
        ['para', ['ul', 'ol', 'paragraph']],
        ['table', ['table']],
        ['insert', ['link', 'picture', 'video']],
        ['view', ['fullscreen', 'codeview', 'help']]
      ]
    });
				// CKEDITOR.replace('description');

    $('#description').summernote({
      placeholder: 'Enter Content Here...',
      tabsize: 2,
      height: 200,
      toolbar: [
        ['style', ['style']],
        ['font', ['bold', 'underline', 'clear']],
        ['color', ['color']],
        ['para', ['ul', 'ol', 'paragraph']],
        ['table', ['table']],
        ['insert', ['link', 'picture', 'video']],
        ['view', ['fullscreen', 'codeview', 'help']]
      ]
    });
            </script>
<script>
	function delete_product_image(id,row_id){
var retVal = confirm("Are you sure want to delete.");
	if( retVal == true ){	
	$.ajax({
	  url:'ajax/delete_products_details.php',
	  type:'post',
	  data:{'id':id},
	  success:function(data){   
		  $(".image_rows_"+row_id).hide('');
	   },
	  });
	}else{
        return false;
   }
}
</script>

<script>
function get_sub_category(category_id){
	if(category_id!=""){
	$.ajax({
	  url:'ajax/get_sub_category.php',
	  type:'post',
	  data:{'category_id':category_id},
	  success:function(data){   
		  $("#sub_category_id").html(data);	
	     },
	  });			
	 }else{
	 $("#sub_category_id").html('<option value="">SELECT</option>');	 
	  }
	}
</script>



            
            