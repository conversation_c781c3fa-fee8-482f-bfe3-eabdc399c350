<?php 
session_start();
$session_id=session_id();
include('../admin/lib/db_connection.php');
include('../admin/inc/resize-class.php');
date_default_timezone_set("Asia/Kolkata");
$date=date('Y-m-d H:i:s');


$sel=dbQuery("SELECT * FROM tabl_services WHERE id='".$_REQUEST['service_id']."'");
$num=dbNumRows($sel);
if($num>0){
$res=dbFetchAssoc($sel);

$sel1=dbQuery("SELECT * FROM tabl_service_cart WHERE service_id='".$res['id']."' AND vendor_id='".$res['vendor_id']."' AND user_id='".$_SESSION['user_id']."' AND session_id='".$session_id."'");
$num1=dbNumRows($sel1);
if($num1>0){	

dbQuery("UPDATE tabl_service_cart SET price='".$res['price']."',service_date='".$_REQUEST['service_date']."',service_time='".$_REQUEST['service_time']."',name='".mysqli_real_escape_string($con,$_REQUEST['name'])."',phone='".mysqli_real_escape_string($con,$_REQUEST['phone'])."',email='".mysqli_real_escape_string($con,$_REQUEST['email'])."',message='".mysqli_real_escape_string($con,$_REQUEST['message'])."' WHERE service_id='".$res['id']."' AND vendor_id='".$res['vendor_id']."' AND user_id='".$_SESSION['user_id']."' AND session_id='".$session_id."'");

}else{
dbQuery("INSERT INTO tabl_service_cart SET user_id='".$_SESSION['user_id']."',session_id='".$session_id."',service_id='".$res['id']."',vendor_id='".$res['vendor_id']."',price='".$res['price']."',service_date='".$_REQUEST['service_date']."',service_time='".$_REQUEST['service_time']."',message='".mysqli_real_escape_string($con,$_REQUEST['message'])."',name='".mysqli_real_escape_string($con,$_REQUEST['name'])."',phone='".mysqli_real_escape_string($con,$_REQUEST['phone'])."',email='".mysqli_real_escape_string($con,$_REQUEST['email'])."'");	
	}
echo 1;	
}else{
echo 2;	
}
?>