<?php 
session_start();
include('../admin/lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");
$date=date('Y-m-d H:i:s');


$sel=dbQuery("SELECT * FROM tabl_address WHERE user_id='".$_SESSION['customer_id']."'");
$num=dbNumRows($sel);
if($num==0){
	$is_default=1;
}else{
	if($_REQUEST['is_default']==1){
		  dbQuery("UPDATE tabl_address SET is_default=0");
		  $is_default=1;
    	}else{
		$is_default=0;	
	}
}

dbQuery("UPDATE tabl_address SET address='".mysqli_real_escape_string($con,$_REQUEST['address'])."',area='".mysqli_real_escape_string($con,$_REQUEST['area'])."',landmark='".mysqli_real_escape_string($con,$_REQUEST['landmark'])."',pincode='".mysqli_real_escape_string($con,$_REQUEST['pincode'])."',city='".mysqli_real_escape_string($con,$_REQUEST['city'])."',state='".mysqli_real_escape_string($con,$_REQUEST['state'])."',is_default='".$is_default."' WHERE user_id='".$_SESSION['customer_id']."' AND id='".$_REQUEST['address_id']."'");
echo 1;
?>