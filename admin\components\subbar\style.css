/* SUBBAR */
.air__subbar {
    background: #fff;
    min-height: 4.26rem;
    border-bottom: 1px solid #e4e9f0;
    padding: 1rem 1.33rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
}

.air__subbar__divider {
    width: 1px;
    background-color: #e4e9f0;
    -ms-flex-item-align: stretch;
        align-self: stretch;
    -webkit-box-flex: 0;
        -ms-flex-positive: 0;
            flex-grow: 0;
    -ms-flex-negative: 0;
        flex-shrink: 0;
}

.air__subbar__breadcrumbs {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
    font-size: 1.2rem;
}

.air__subbar__breadcrumb {
    position: relative;
    margin-right: 2rem;
}

.air__subbar__breadcrumb::after {
    content: '-';
    position: absolute;
    right: -1.2rem;
    bottom: 0;
}

.air__subbar__breadcrumb:last-child {
    margin-right: 0;
}

.air__subbar__breadcrumb:last-child::after {
    display: none;
}

.air__subbar__breadcrumbLink {
    display: block;
}

.air__subbar__breadcrumbLink--current {
    color: #161537;
    font-weight: 700;
    pointer-events: none;
}

.air__subbar__amount {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
}

.air__subbar__amountText {
    font-size: 0.8rem;
    color: #c3bedc;
    -ms-flex-negative: 0;
        flex-shrink: 0;
    margin-right: 1rem;
    margin-bottom: 0;
    text-align: right;
    text-transform: uppercase;
}

.air__subbar__amountValue {
    color: #161537;
    font-size: 1.2rem;
    display: block;
    font-weight: 700;
    line-height: 1;
}

.air__subbar__amountGraph {
    -ms-flex-negative: 0;
        flex-shrink: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: end;
        -ms-flex-align: end;
            align-items: flex-end;
}

.air__subbar__amountGraphItem {
    margin-right: 0.4rem;
    width: 0.33rem;
    border-radius: 3px;
    background-color: #1b55e3;
}

.air__subbar__amountGraphItem:last-child {
    margin-right: 0;
}
