<?php

session_start();

include('../admin/lib/db_connection.php');

date_default_timezone_set("Asia/Kolkata");

$date = date('Y-m-d H:i:s');



$otp = rand(1000, 9999);

$sel_email = dbQuery("SELECT * FROM tabl_vendor WHERE email='" . $_REQUEST['email'] . "'");

$num1 = dbNumRows($sel_email);

if ($num1 > 0) {

  echo 2;

  die();
} else {



  $sel_phone = dbQuery("SELECT * FROM tabl_vendor WHERE phone='" . $_REQUEST['phone'] . "'");

  $num2 = dbNumRows($sel_phone);

  if ($num2 > 0) {

    echo 3;

    die();
  } else {


    dbQuery("DELETE FROM tabl_vendor_otp WHERE phone='" . $_REQUEST['phone'] . "'");
    dbQuery("DELETE FROM tabl_temp_vendor WHERE phone='" . $_REQUEST['phone'] . "'");

    dbQuery("INSERT INTO tabl_vendor_otp SET otp='" . $otp . "',phone='" . $_REQUEST['phone'] . "'");

    dbQuery("INSERT INTO tabl_temp_vendor SET name='" . mysqli_real_escape_string($con, $_REQUEST['name']) . "',phone='" . $_REQUEST['phone'] . "',email='" . $_REQUEST['email'] . "',password='" . md5($_REQUEST['password']) . "',city_id='" . $_REQUEST['city_id'] . "',date_added='" . $date . "'");

    $curl = curl_init();
    curl_setopt_array($curl, array(
      CURLOPT_URL => 'http://hindit.co.in/API/pushsms.aspx?loginID=T1BOOMPASS&password=boom123&mobile=' . $_REQUEST['phone'] . '&text=Your%20otp%20' . $otp . '%20for%20login%20in%20BOOMPASS%20Thanks%20for%20registering%20with%20BOOMPASS.&senderid=BOOMPS&route_id=2&Unicode=0&IP=x.x.x.x&Template_id=1707169579389956699',
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_ENCODING => '',
      CURLOPT_MAXREDIRS => 10,
      CURLOPT_TIMEOUT => 0,
      CURLOPT_FOLLOWLOCATION => true,
      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
      CURLOPT_CUSTOMREQUEST => 'GET',
    ));
    $response = curl_exec($curl);
    curl_close($curl);

    $_SESSION['vendor_login_phone'] = $_REQUEST['phone'];

    echo 1;
  }
}
