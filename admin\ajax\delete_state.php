<?php
session_start();
include('../lib/db_connection.php');
include('../lib/auth.php');

if(isset($_POST['id'])){
    $id = $_POST['id'];
    
    // Check if state is being used in cities
    $check_cities = dbQuery("SELECT COUNT(*) as count FROM tabl_city WHERE state_id = '".$id."'");
    $cities_count = dbFetchAssoc($check_cities);
    
    if($cities_count['count'] > 0){
        echo "error_cities_exist";
    } else {
        $delete = dbQuery("DELETE FROM tabl_state WHERE id = '".$id."'");
        if($delete){
            echo "success";
        } else {
            echo "error";
        }
    }
}
?>
