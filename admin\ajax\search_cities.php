<?php
session_start();
include('../lib/db_connection.php');
include('../lib/auth.php');

if(isset($_POST['search'])){
    $search = mysqli_real_escape_string($con, $_POST['search']);
    
    // Search cities with state names, limit to 10 results for performance
    $query = "SELECT tabl_city.id, tabl_city.city, tabl_state.state_name 
              FROM tabl_city 
              INNER JOIN tabl_state ON tabl_city.state_id = tabl_state.id 
              WHERE tabl_city.status = 1 
              AND tabl_state.status = 1 
              AND (tabl_city.city LIKE '%".$search."%' OR tabl_state.state_name LIKE '%".$search."%')
              ORDER BY tabl_city.city ASC 
              LIMIT 10";
    
    $result = dbQuery($query);
    $cities = array();
    
    while($row = dbFetchAssoc($result)){
        $cities[] = array(
            'id' => $row['id'],
            'city' => $row['city'],
            'state_name' => $row['state_name']
        );
    }
    
    echo json_encode($cities);
} else {
    echo json_encode(array());
}
?>
