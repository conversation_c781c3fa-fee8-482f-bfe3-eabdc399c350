<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Manual Payment Flow</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .payment-settings {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .qr-code {
            text-align: center;
            margin: 20px 0;
        }
        
        .qr-code img {
            max-width: 200px;
            border: 2px solid #007bff;
            border-radius: 10px;
            padding: 10px;
        }
        
        .upi-info {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .flow-step {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
        
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            display: inline-block;
            margin: 5px;
        }
        
        .btn:hover {
            background: #0056b3;
            color: white;
        }
        
        .status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status.success { background: #d4edda; color: #155724; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Manual Payment System Test</h1>
        
        <h2>1. Payment Settings Configuration</h2>
        <?php
        // Include database connection
        include('admin/lib/db_connection.php');
        
        // Fetch settings
        $sel = dbQuery("SELECT * FROM tabl_setting WHERE id='1'");
        $res = dbFetchAssoc($sel);
        
        if($res) {
        ?>
        
        <div class="payment-settings">
            <h3>Current Payment Settings</h3>
            
            <?php if(!empty($res['upi_id'])): ?>
            <div class="upi-info">
                <h4>UPI Payment</h4>
                <p><strong>UPI ID:</strong> <?php echo htmlspecialchars($res['upi_id']); ?></p>
                <span class="status success">✓ Configured</span>
            </div>
            <?php else: ?>
            <div class="upi-info">
                <p><em>UPI ID not configured</em></p>
                <span class="status warning">⚠ Not Configured</span>
            </div>
            <?php endif; ?>
            
            <?php if(!empty($res['qr_code'])): ?>
            <div class="qr-code">
                <h4>QR Code for Payment</h4>
                <img src="assets/images/qr_code/<?php echo $res['qr_code']; ?>" alt="Payment QR Code">
                <br><span class="status success">✓ Configured</span>
            </div>
            <?php else: ?>
            <div class="qr-code">
                <p><em>QR Code not uploaded</em></p>
                <span class="status warning">⚠ Not Configured</span>
            </div>
            <?php endif; ?>
        </div>
        
        <?php
        } else {
            echo '<div class="payment-settings"><p>No settings found. Please configure settings in admin panel.</p></div>';
        }
        ?>
        
        <h2>2. Payment Flow Test</h2>
        
        <div class="flow-step">
            <h4>Step 1: Configure Payment Settings</h4>
            <p>Admin can set UPI ID and upload QR code for manual payments</p>
            <a href="admin/setting.php" class="btn">Go to Admin Settings</a>
        </div>
        
        <div class="flow-step">
            <h4>Step 2: Customer Payment Process</h4>
            <p>Customer selects manual payment option during checkout</p>
            <a href="payment.php" class="btn">Test Payment Page</a>
        </div>
        
        <div class="flow-step">
            <h4>Step 3: Manual Payment Page</h4>
            <p>Customer sees QR code, enters transaction ID and uploads screenshot</p>
            <a href="manual_payment.php" class="btn">Test Manual Payment</a>
            <small style="display: block; margin-top: 5px; color: #666;">
                Note: This requires an active order session
            </small>
        </div>
        
        <div class="flow-step">
            <h4>Step 4: Admin Review</h4>
            <p>Admin can view all manual payments and approve them</p>
            <a href="admin/manual_payments.php" class="btn">View Manual Payments</a>
        </div>
        
        <h2>3. Recent Manual Payment Orders</h2>
        
        <?php
        // Fetch recent manual payment orders
        $orders = dbQuery("SELECT tabl_order.*, tabl_user.name as customer_name 
                          FROM tabl_order 
                          INNER JOIN tabl_user ON tabl_order.customer_id = tabl_user.id 
                          WHERE tabl_order.payment_type = '3' 
                          ORDER BY tabl_order.id DESC 
                          LIMIT 5");
        
        if(dbNumRows($orders) > 0) {
        ?>
        <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
            <thead>
                <tr style="background: #f8f9fa;">
                    <th style="padding: 10px; border: 1px solid #ddd;">Order ID</th>
                    <th style="padding: 10px; border: 1px solid #ddd;">Customer</th>
                    <th style="padding: 10px; border: 1px solid #ddd;">Amount</th>
                    <th style="padding: 10px; border: 1px solid #ddd;">Transaction ID</th>
                    <th style="padding: 10px; border: 1px solid #ddd;">Status</th>
                    <th style="padding: 10px; border: 1px solid #ddd;">Date</th>
                </tr>
            </thead>
            <tbody>
                <?php while($order = dbFetchAssoc($orders)): ?>
                <tr>
                    <td style="padding: 10px; border: 1px solid #ddd;">#<?php echo $order['id']; ?></td>
                    <td style="padding: 10px; border: 1px solid #ddd;"><?php echo $order['customer_name']; ?></td>
                    <td style="padding: 10px; border: 1px solid #ddd;">₹<?php echo number_format($order['total_price'], 2); ?></td>
                    <td style="padding: 10px; border: 1px solid #ddd;">
                        <?php echo !empty($order['transaction_id']) ? $order['transaction_id'] : '<em>Pending</em>'; ?>
                    </td>
                    <td style="padding: 10px; border: 1px solid #ddd;">
                        <?php 
                        if($order['order_status_id'] == 0) {
                            echo '<span class="status warning">Pending</span>';
                        } elseif($order['order_status_id'] == 1) {
                            echo '<span class="status success">Confirmed</span>';
                        } else {
                            echo '<span class="status info">Processing</span>';
                        }
                        ?>
                    </td>
                    <td style="padding: 10px; border: 1px solid #ddd;">
                        <?php echo date('d M Y, h:i A', strtotime($order['date_added'])); ?>
                    </td>
                </tr>
                <?php endwhile; ?>
            </tbody>
        </table>
        <?php
        } else {
            echo '<p>No manual payment orders found.</p>';
        }
        ?>
        
        <h2>4. File Structure</h2>
        <div class="flow-step">
            <h4>QR Code Storage</h4>
            <p><code>assets/images/qr_code/</code> - QR code images uploaded by admin</p>
        </div>
        
        <div class="flow-step">
            <h4>Payment Screenshots</h4>
            <p><code>assets/images/payment_screenshots/</code> - Screenshots uploaded by customers</p>
        </div>
        
        <div class="flow-step">
            <h4>Database Tables</h4>
            <p><code>tabl_setting</code> - Stores UPI ID and QR code filename</p>
            <p><code>tabl_order</code> - Stores transaction ID and screenshot filename</p>
        </div>
        
    </div>
</body>
</html>
