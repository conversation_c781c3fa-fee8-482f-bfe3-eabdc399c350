<?php 
session_start();
include('../admin/lib/db_connection.php');
include('../admin/inc/resize-class.php');
date_default_timezone_set("Asia/Kolkata");
$date=date('Y-m-d H:i:s');
$sel=dbQuery("SELECT * FROM tabl_user WHERE phone='".$_REQUEST['phone']."' AND id!='".$_SESSION['user_id']."'");
$num=dbNumRows($sel);
if($num>0){
	echo 2;
	die();
}else{	

if($_FILES["user_image"]["name"]!=""){		  
		  $target_dir = "../img/user/";
		  $name = rand(10000,1000000);
		  $extension = pathinfo($_FILES["user_image"]["name"], PATHINFO_EXTENSION);
		  $new_name=$name.".".$extension;
          $target_file = $target_dir . $name.".".$extension;

	$imageFileType = strtolower(pathinfo($target_file,PATHINFO_EXTENSION));	
if($imageFileType != "jpg" && $imageFileType != "png" && $imageFileType != "jpeg") {
    die("This is not valid image. Please try again.");
} else{  
	move_uploaded_file($_FILES["user_image"]["tmp_name"], $target_file);
	 	 $target_path="../img/user/".$new_name;
			$resizeObj = new resize("../img/user/".$new_name);
			$resizeObj -> resizeImage(100, 100, 'crop');
			$resizeObj -> saveImage("../img/user/thumb-100/".$new_name, 100);
			
        $upd=dbQuery("UPDATE tabl_user SET user_image='".$new_name."' WHERE id='".$_SESSION['user_id']."'");
   }
   
}

dbQuery("UPDATE tabl_user SET name='".$_REQUEST['name']."',phone='".mysqli_real_escape_string($con,$_REQUEST['phone'])."',email='".mysqli_real_escape_string($con,$_REQUEST['email'])."' WHERE  id='".$_SESSION['user_id']."'");
echo 1;	
}
?>