<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1">
<meta http-equiv="x-ua-compatible" content="ie=edge">
<title>Project Womaniya Delivery Admin | Authentication</title>
<link href="favicon.png" rel="shortcut icon">
<link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:400,400i,700,700i,900" rel="stylesheet">

<!-- VENDORS -->

<link rel="stylesheet" type="text/css" href="vendors/bootstrap/dist/css/bootstrap.css">
<link rel="stylesheet" type="text/css" href="vendors/font-feathericons/dist/feather.css">
<link rel="stylesheet" type="text/css" href="vendors/font-awesome/css/font-awesome.min.css">
<link rel="stylesheet" type="text/css" href="vendors/font-linearicons/style.css">
<link rel="stylesheet" type="text/css" href="vendors/font-icomoon/style.css">
<script src="vendors/jquery/dist/jquery.min.js"></script>
<script src="vendors/bootstrap/dist/js/bootstrap.js"></script>
<script src="vendors/jquery-mousewheel/jquery.mousewheel.min.js"></script>
<script src="vendors/perfect-scrollbar/js/perfect-scrollbar.jquery.js"></script>
<script src="vendors/chartist/dist/chartist.min.js"></script>
<script src="vendors/chart.js/dist/Chart.min.js"></script>
<script src="vendors/jqvmap/dist/jquery.vmap.min.js"></script>
<script src="vendors/jqvmap/dist/maps/jquery.vmap.usa.js"></script>
<script src="vendors/chartist-plugin-tooltip/dist/chartist-plugin-tooltip.min.js"></script>
<script src="vendors/d3/d3.min.js"></script>
<script src="vendors/d3-dsv/dist/d3-dsv.js"></script>
<script src="vendors/d3-time-format/dist/d3-time-format.js"></script>
<script src="vendors/Stickyfill/dist/stickyfill.min.js"></script>

<!-- AIR UI HTML ADMIN TEMPLATE MODULES-->

<link rel="stylesheet" type="text/css" href="components/vendors/style.css">
<link rel="stylesheet" type="text/css" href="components/core/style.css">
<link rel="stylesheet" type="text/css" href="components/widgets/style.css">
<link rel="stylesheet" type="text/css" href="components/system/style.css">

<!-- PRELOADER STYLES-->

</head>

<body>
<div class="air__auth">
  <div class="pt-5 pb-5 d-flex align-items-end mt-auto"><h2>Project Womaniya</h2></div>
  <div class="air__auth__container pl-5 pr-5 pt-5 pb-5 bg-white text-center">
    <div class="text-dark font-size-30 mb-4">Log In</div>
    <form class="mb-4" id="loginform">
      <div class="form-group mb-4">
        <input type="text" id="user" name="user" class="form-control" placeholder="User"  required/>
      </div>
      <div class="form-group mb-4">
        <input type="password" id="password" name="password" class="form-control" placeholder="Password"  required/>
      </div>
      
      <button class="text-center btn btn-success w-100 font-weight-bold font-size-18 submit" type="submit"> Log In </button>
    </form>
    <a href="#" class="text-blue font-weight-bold font-size-18">Forgot password?</a>
    <div class="loader_login"></div>
  </div>
  <div class="mt-auto pb-5 pt-5">
    
  </div>
</div>
</body>
</html>
<script>

 $("#loginform").submit(function(e) {

	$(".submit").attr('disabled',true);

	$(".loader_login").html('<img src="loader.gif" width="20"> please wait...');	

	 $.ajax({

	  url:'ajax/login.php',

	  type:'post',

	  data:$("#loginform").serialize(),

	  success:function(data){

	if(data==1)

		{        

		  $(".loader").hide();

		  $(location).attr('href','home.php');

			}

		else{

			$(".submit").attr('disabled',false);

	  $(".loader_login").html('<div class="alert alert-danger" role="alert">Either username or Password are wrong!</div>');	

	setTimeout(function() {

      $(".loader_login").html('');	 



					}, 8000);	

			}

		},

	  });

   e.preventDefault(); // avoid to execute the actual submit of the form.



});



</script>