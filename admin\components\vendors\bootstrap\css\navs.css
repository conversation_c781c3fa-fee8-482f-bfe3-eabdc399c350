/* NAVS */
.nav .nav-link.disabled {
    opacity: 0.4;
}

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
    background: #1b55e3;
}

.nav-tabs .nav-link.active {
    background: transparent;
}

.nav-tabs-noborder {
    border-bottom: 0;
}

.nav-tabs-stretched {
    -webkit-box-align: stretch;
        -ms-flex-align: stretch;
            align-items: stretch;
}

.nav-tabs-stretched .nav-item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}

.nav-tabs-stretched .nav-link {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
}

.nav-tabs-line .nav-item:last-child .nav-link {
    margin-right: 0;
}

.nav-tabs-line .nav-item.show .nav-link {
    border-color: transparent;
}

.nav-tabs-line .nav-link {
    padding-left: 0;
    padding-right: 0;
    margin-right: 1.33rem;
}

.nav-tabs-line .nav-link:hover, .nav-tabs-line .nav-link:focus, .nav-tabs-line .nav-link:active {
    border-color: transparent;
}

.nav-tabs-line .nav-link.active {
    border-color: transparent;
    position: relative;
    color: #1b55e3;
}

.nav-tabs-line .nav-link.active:after {
    position: absolute;
    bottom: -1px;
    left: 0;
    content: '';
    display: block;
    height: 1px;
    width: 100%;
    background: #1b55e3;
}

.nav-tabs-line-bold .nav-link.active:after {
    height: 7px;
    bottom: -4px;
    border-radius: 7px;
}
