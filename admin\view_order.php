<?php 
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page=6;
$sub_page=0;
date_default_timezone_set("Asia/Kolkata");
$date=date('Y-m-d');
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1">
<meta http-equiv="x-ua-compatible" content="ie=edge">
<title><?php echo SITE;?> | View Orders </title>
<link href="favicon.png" rel="shortcut icon">
<link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:400,400i,700,700i,900" rel="stylesheet">
<!-- VENDORS -->
<link rel="stylesheet" type="text/css" href="vendors/bootstrap/dist/css/bootstrap.css">
<link rel="stylesheet" type="text/css" href="vendors/font-feathericons/dist/feather.css">
<link rel="stylesheet" type="text/css" href="vendors/font-awesome/css/font-awesome.min.css">
<link rel="stylesheet" type="text/css" href="vendors/font-linearicons/style.css">
<link rel="stylesheet" type="text/css" href="vendors/font-icomoon/style.css">
<link rel="stylesheet" type="text/css" href="vendors/perfect-scrollbar/css/perfect-scrollbar.css">
<link rel="stylesheet" type="text/css" href="vendors/chart.js/dist/Chart.min.css">
<link rel="stylesheet" type="text/css" href="vendors/jqvmap/dist/jqvmap.min.css">
<link rel="stylesheet" type="text/css" href="vendors/c3/c3.min.css">
<link rel="stylesheet" type="text/css"
    href="cdn.datatables.net/v/bs4/dt-1.10.18/fc-3.2.5/r-2.2.2/datatables.min.css" />
<link rel="stylesheet" type="text/css" href="vendors/tempus-dominus-bs4/build/css/tempusdominus-bootstrap-4.min.css">
<link rel="stylesheet" type="text/css" href="vendors/fullcalendar/dist/fullcalendar.min.css">
<link rel="stylesheet" type="text/css" href="vendors/owl.carousel/dist/assets/owl.carousel.min.css">
<link rel="stylesheet" type="text/css" href="vendors/ionrangeslider/css/ion.rangeSlider.css">
<link rel="stylesheet" type="text/css" href="vendors/bootstrap-sweetalert/dist/sweetalert.css">
<link rel="stylesheet" type="text/css" href="vendors/nprogress/nprogress.css">
<link rel="stylesheet" type="text/css" href="vendors/summernote/dist/summernote.css">
<link rel="stylesheet" type="text/css" href="vendors/dropify/dist/css/dropify.min.css">
<link rel="stylesheet" type="text/css" href="vendors/jquery-steps/demo/css/jquery.steps.css">
<link rel="stylesheet" type="text/css" href="vendors/select2/dist/css/select2.min.css">
<link rel="stylesheet" type="text/css" href="vendors/bootstrap-select/dist/css/bootstrap-select.min.css">
<script src="vendors/jquery/dist/jquery.min.js"></script>
<script src="vendors/popper.js/dist/umd/popper.js"></script>
<script src="vendors/bootstrap/dist/js/bootstrap.js"></script>
<script src="vendors/jquery-mousewheel/jquery.mousewheel.min.js"></script>
<script src="vendors/perfect-scrollbar/js/perfect-scrollbar.jquery.js"></script>
<script src="vendors/chartist/dist/chartist.min.js"></script>
<script src="vendors/chart.js/dist/Chart.min.js"></script>
<script src="vendors/jqvmap/dist/jquery.vmap.min.js"></script>
<script src="vendors/jqvmap/dist/maps/jquery.vmap.usa.js"></script>
<script src="vendors/chartist-plugin-tooltip/dist/chartist-plugin-tooltip.min.js"></script>
<script src="vendors/d3/d3.min.js"></script>
<script src="vendors/d3-dsv/dist/d3-dsv.js"></script>
<script src="vendors/d3-time-format/dist/d3-time-format.js"></script>
<script src="vendors/c3/c3.min.js"></script>
<script src="vendors/peity/jquery.peity.min.js"></script>
<script type="text/javascript" src="cdn.datatables.net/v/bs4/dt-1.10.18/fc-3.2.5/r-2.2.2/datatables.min.js"></script>
<script src="vendors/editable-table/mindmup-editabletable.js"></script>
<script src="vendors/moment/min/moment.min.js"></script>
<script src="vendors/tempus-dominus-bs4/build/js/tempusdominus-bootstrap-4.min.js"></script>
<script src="vendors/fullcalendar/dist/fullcalendar.min.js"></script>
<script src="vendors/owl.carousel/dist/owl.carousel.min.js"></script>
<script src="vendors/ionrangeslider/js/ion.rangeSlider.min.js"></script>
<script src="vendors/remarkable-bootstrap-notify/dist/bootstrap-notify.min.js"></script>
<script src="vendors/bootstrap-sweetalert/dist/sweetalert.min.js"></script>
<script src="vendors/nprogress/nprogress.js"></script>
<script src="vendors/summernote/dist/summernote.min.js"></script>
<script src="vendors/nestable/jquery.nestable.js"></script>
<script src="vendors/jquery-typeahead/dist/jquery.typeahead.min.js"></script>
<script src="vendors/autosize/dist/autosize.min.js"></script>
<script src="vendors/bootstrap-show-password/dist/bootstrap-show-password.min.js"></script>
<script src="vendors/dropify/dist/js/dropify.min.js"></script>
<script src="vendors/html5-form-validation/dist/jquery.validation.min.js"></script>
<script src="vendors/jquery-steps/build/jquery.steps.min.js"></script>
<script src="vendors/jquery-mask-plugin/dist/jquery.mask.min.js"></script>
<script src="vendors/select2/dist/js/select2.full.min.js"></script>
<script src="vendors/bootstrap-select/dist/js/bootstrap-select.min.js"></script>
<script src="vendors/d3-dsv/dist/d3-dsv.js"></script>
<script src="vendors/d3-time-format/dist/d3-time-format.js"></script>
<script src="vendors/techan/dist/techan.min.js"></script>
<script src="vendors/Stickyfill/dist/stickyfill.min.js"></script>

<!-- AIR UI HTML ADMIN TEMPLATE MODULES-->
<link rel="stylesheet" type="text/css" href="components/vendors/style.css">
<link rel="stylesheet" type="text/css" href="components/core/style.css">
<link rel="stylesheet" type="text/css" href="components/widgets/style.css">
<link rel="stylesheet" type="text/css" href="components/system/style.css">
<link rel="stylesheet" type="text/css" href="components/menu-left/style.css">
<link rel="stylesheet" type="text/css" href="components/menu-top/style.css">
<link rel="stylesheet" type="text/css" href="components/footer/style.css">
<link rel="stylesheet" type="text/css" href="components/footer-dark/style.css">
<link rel="stylesheet" type="text/css" href="components/topbar/style.css">
<link rel="stylesheet" type="text/css" href="components/topbar-dark/style.css">
<link rel="stylesheet" type="text/css" href="components/subbar/style.css">
<link rel="stylesheet" type="text/css" href="components/sidebar/style.css">
<link rel="stylesheet" type="text/css" href="components/chat/style.css">
<link rel="stylesheet" type="text/css" href="components/apps/style.css">
<link rel="stylesheet" type="text/css" href="components/apps/style.css">
<link rel="stylesheet" type="text/css" href="components/extra-apps/style.css">
<link rel="stylesheet" type="text/css" href="components/ecommerce/style.css">
<link rel="stylesheet" type="text/css" href="components/dashboards/crypto-terminal/style.css">
<script src="components/core/index.js"></script>
<script src="components/menu-left/index.js"></script>
<script src="components/menu-top/index.js"></script>
<script src="components/sidebar/index.js"></script>
<script src="components/topbar/index.js"></script>
<script src="components/chat/index.js"></script>

<!-- PRELOADER STYLES-->

</head>
<body class="air__menu--blue air__menu__submenu--blue">
<div class="air__initialLoading"></div>
<div class="air__layout">
  <div class="air__menuTop">
    <div class="air__menuTop__outer">
      <div class="air__menuTop__mobileToggleButton air__menuTop__mobileActionToggle"> <span></span> </div>
     <a href="home.php" class="air__menuTop__logo">
          <h1 style="color:#FFF"><?php echo SITE; ?></h1>
        </a>
      <?php include('inc/__menu.php'); ?>
    </div>
  </div>
  <div class="air__menuTop__backdrop air__menuTop__mobileActionToggle"></div>
  <div class="air__layout">
    <?php include('inc/__header.php');?>
    <div class="air__layout__content">
      <div class="air__utils__content">
        <div class="air__utils__heading">
          <h5>Orders: View</h5>
          <nav aria-label="breadcrumb" style="float: right;margin-top: -35px;">
            <ol class="breadcrumb">
              <li class="breadcrumb-item"><a href="home.php">Home</a></li>
             <li class="breadcrumb-item"><a href="order.php">Order</a></li>
              <li class="breadcrumb-item active" aria-current="page">View Orders</li>
            </ol>
          </nav>
        </div>
        <div class="row">
          <div class="col-lg-12">
            <div class="card">
              <div class="card-body">
                <div class="row">
              <div class="col-lg-6 col-md-12">
                 <div class="invoice-card">
                 <div class="invoice-img">
                  <h4> Order  <span class="font-style text-theme">Summary</span></h4>
                    <h2>Project Womaniya</h2>
                 </div>
  <div class="invoice-title">
    <div id="main-title">
      
      
    </div>
    <div class="invoice-list">
       <?php $order=dbQuery("SELECT * FROM tabl_order WHERE id='".$_REQUEST['id']."'");
          $res_order=dbFetchAssoc($order);
		  $state=dbQuery("SELECT * FROM tabl_state WHERE id='".$res_order['state']."'");
		    $res_state=dbFetchAssoc($state);
	   ?>
	   <ul>
          <li>Order No. <span>#<?php echo $res_order['id'];?></span></li>
           <li>Order Status. <span><?php if($res_order['order_status_id']==0){
	$status='Pending';
}elseif($res_order['order_status_id']==1){
	$status='Complete';	
}elseif($res_order['order_status_id']==2){

	$status='Processed';	
}else{
	$status='Order Failed';	
}
echo $status;
?></span></li>
          <li>Date <span><?php echo date('d/m/Y',strtotime($res_order['date_added']));?></span></li>
          <li>Time <span><?php echo date('g:i a',strtotime($res_order['date_added']));?></span></li>
          <li>Name <span><?php echo $res_order['name'];?></span></li>
          <li>Phone No. <span><?php echo $res_order['phone'];?></span></li>
           <li>Email <span><?php echo $res_order['email'];?></span></li>
           <li>Address <span><?php echo $res_order['address'];?>,<?php echo $res_order['city'];?>,<?php echo $res_state['state_name'];?>-<?php echo $res_order['pincode'];?></span></li> 

       </ul>
    </div>
    
  </div>
  
  <div class="invoice-details" style="margin-top:10px;">
    <table class="table-bordered" width="100%" cellpadding="10" cellspacing="10">
      <thead>
        <tr>
          <th>PRODUCT</th>
          <th>Quantity</th>
          <th>PRICE</th>
          <th>TOTAL</th>
        </tr>
      </thead>
      
      <tbody>
      <?php $order_details=dbQuery("SELECT * FROM tabl_order_product WHERE order_id='".$res_order['id']."'");
          while($res_order_details=dbFetchAssoc($order_details)){
			    $qty_type=dbQuery("SELECT * FROM tabl_product_type WHERE id='".$res_order_details['qty_type']."'");
				$res_qty_type=dbFetchAssoc($qty_type);
			   
	   ?> 
	   <tr class="row-data">
          <td><?php echo $res_order_details['name'];?><br/><span style="font-size:13px;color:#F00;"><?php echo $res_qty_type['p_name']; ?></span></td>
          <td id="unit"><?php echo $res_order_details['qty'];?></td>
          <td><?php echo $res_order_details['price'];?></td>
          <td><?php echo $res_order_details['total'];?></td>
        </tr>
		  <?php } ?>

<?php $cart_promo=dbQuery("SELECT * FROM tabl_order_promo WHERE order_id='".$res_order['id']."'");
		        $cart_promo_num=dbNumRows($cart_promo);
				$res_cart_promo=dbFetchAssoc($cart_promo);
		 if($cart_promo_num>0){?>
                 <tr class="calc-row">
          <th colspan="3">Discount</th>
          <td>(-)<?php echo number_format($res_cart_promo['discount'], 2, '.', '');?></td>
        </tr>
        <?php } ?>

        <tr class="calc-row">
          <th colspan="3">Total</th>
          <td><?php echo number_format($res_order['total_price'], 2, '.', '');?></td>
        </tr>
      </tbody>
    </table>
  </div>
  
 <!--  <div class="invoice-footer">
    <button class="btn btn-secondary" id="later">LATER</button>
    <button class="btn btn-primary">PAY NOW</button>
  </div> -->
</div>
              </div>
           </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <?php include('inc/__footer.php'); ?>
  </div>
</div>
</body>
</html>
<script>
  ;(function($) {
    'use strict'
    $(function() {
      $('#example1').DataTable({
        responsive: true,
      })

      $('#example2').DataTable({
        autoWidth: true,
        scrollX: true,
        fixedColumns: true,
      })

      $('#example3').DataTable({
        autoWidth: true,
        scrollX: true,

        fixedColumns: true,
      })
    })
  })(jQuery)
</script>
<script>
  ;(function($) {
    'use strict'
    $(function() {
      $('#form-validation').validate({
        submit: {
          settings: {
            inputContainer: '.form-group',
            errorListClass: 'form-control-error',
            errorClass: 'has-danger',
          },
        },
      })

      $('#form-validation .remove-error').on('click', function() {
        $('#form-validation').removeError()
      })

      $('#form-validation-simple').validate({
        submit: {
          settings: {
            inputContainer: '.form-group',
            errorListClass: 'form-control-error-list',
            errorClass: 'has-danger',
          },
        },
      })

      $('#form-validation-simple .remove-error').on('click', function() {
        $('#form-validation-simple').removeError()
      })

      $('.select2').select2()
    })
  })(jQuery)
</script>
<script>
   function isNumber(evt) {
        var iKeyCode = (evt.which) ? evt.which : evt.keyCode
        if (iKeyCode != 46 && iKeyCode > 31 && (iKeyCode < 48 || iKeyCode > 57))
            return false;

        return true;
    }
</script>
<script>
    function delete_plan(id)
	{
	var retVal = confirm("Are you sure want to delete.");
	if( retVal == true ){
      $.ajax({
	  url:'ajax/delete_plan.php',
	  type:'post',
	  data:{'id':id},
	  success:function(data){
		  //alert(data);
		 if(data==1){
			 location.reload();
		  }
   		 },
 	 }); 
   }else{
        return false;
   }
 	
	}
    </script>
<script>
	function change_status(tabl,val,row_id){
		var retVal = confirm("Are you sure want to change status.");
	if( retVal == true ){
      $.ajax({
	  url:'ajax/activate.php',
	  type:'post',
	  data:{'tabl':tabl,'val':val,'row_id':row_id},
	  success:function(data){
		  //alert(data);
		 if(data==1){
			 location.reload();
		  }
   		 },
 	 }); 
   }else{
        return false;
   }
 	
		
	}
	</script>
    
 <style>
 .invoice-title {
    padding: 20px;
    border: 1px solid #eee;
    margin-top: 30px;
}
.invoice-list ul li {
    font-size: 18px;
    font-weight: 500;
    display: flex;
    justify-content: space-between;
    font-family: var(--body-font);
    color: var(--title-color);
    padding: 5px;
    border-bottom: 1px solid #cfcfc15c;
}
.invoice-table{
margin: 0 0 1.5em;
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    border: 1px solid var(--border-color);	
	}
 
 </style>   