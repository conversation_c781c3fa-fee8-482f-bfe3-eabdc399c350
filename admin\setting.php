<?php
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page=1;
$sub_page=0;
date_default_timezone_set("Asia/Kolkata");
$date=date('Y-m-d');
if(isset($_REQUEST['submit'])){
	// Handle QR code upload
	$qr_code_name = '';
	if(isset($_FILES['qr_code']) && $_FILES['qr_code']['error'] == 0) {
		$target_dir = "../assets/images/qr_code/";

		// Create directory if it doesn't exist
		if (!file_exists($target_dir)) {
			mkdir($target_dir, 0777, true);
		}

		$file_extension = strtolower(pathinfo($_FILES['qr_code']['name'], PATHINFO_EXTENSION));
		$allowed_extensions = array('jpg', 'jpeg', 'png', 'gif');

		if(in_array($file_extension, $allowed_extensions)) {
			$qr_code_name = 'qr_code_' . time() . '.' . $file_extension;
			$target_file = $target_dir . $qr_code_name;

			if(move_uploaded_file($_FILES['qr_code']['tmp_name'], $target_file)) {
				// Delete old QR code file if exists
				$old_qr = dbQuery("SELECT qr_code FROM tabl_delivery_setting WHERE id='1'");
				$old_qr_res = dbFetchAssoc($old_qr);
				if(!empty($old_qr_res['qr_code']) && file_exists($target_dir . $old_qr_res['qr_code'])) {
					unlink($target_dir . $old_qr_res['qr_code']);
				}
			} else {
				echo '<script>alert("Error uploading QR code image.");window.location.href="setting.php"</script>';
				exit;
			}
		} else {
			echo '<script>alert("Only JPG, JPEG, PNG & GIF files are allowed for QR code.");window.location.href="setting.php"</script>';
			exit;
		}
	}

	// Update query
	if(!empty($qr_code_name)) {
		$upd=dbQuery("UPDATE tabl_delivery_setting SET site_name='".mysqli_real_escape_string($con,$_REQUEST['site_name'])."',site_email='".$_REQUEST['site_email']."',upi_id='".mysqli_real_escape_string($con,$_REQUEST['upi_id'])."',qr_code='".$qr_code_name."' WHERE id='1'");
	} else {
		$upd=dbQuery("UPDATE tabl_delivery_setting SET site_name='".mysqli_real_escape_string($con,$_REQUEST['site_name'])."',site_email='".$_REQUEST['site_email']."',upi_id='".mysqli_real_escape_string($con,$_REQUEST['upi_id'])."' WHERE id='1'");
	}

	echo '<script>alert("Setting Updated!");window.location.href="setting.php"</script>';
}
 ?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <title><?php echo SITE; ?> | My Account</title>
  <link href="favicon.png" rel="shortcut icon">
  <link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:400,400i,700,700i,900" rel="stylesheet">
  <!-- VENDORS -->
  <link rel="stylesheet" type="text/css" href="vendors/bootstrap/dist/css/bootstrap.css">
  <link rel="stylesheet" type="text/css" href="vendors/font-feathericons/dist/feather.css">
  <link rel="stylesheet" type="text/css" href="vendors/font-awesome/css/font-awesome.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/font-linearicons/style.css">
  <link rel="stylesheet" type="text/css" href="vendors/font-icomoon/style.css">
  <link rel="stylesheet" type="text/css" href="vendors/perfect-scrollbar/css/perfect-scrollbar.css">
  <link rel="stylesheet" type="text/css" href="vendors/chart.js/dist/Chart.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/jqvmap/dist/jqvmap.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/c3/c3.min.css">
  <link rel="stylesheet" type="text/css"
    href="../cdn.datatables.net/v/bs4/dt-1.10.18/fc-3.2.5/r-2.2.2/datatables.min.css" />
  <link rel="stylesheet" type="text/css"
    href="vendors/tempus-dominus-bs4/build/css/tempusdominus-bootstrap-4.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/fullcalendar/dist/fullcalendar.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/owl.carousel/dist/assets/owl.carousel.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/ionrangeslider/css/ion.rangeSlider.css">
  <link rel="stylesheet" type="text/css" href="vendors/bootstrap-sweetalert/dist/sweetalert.css">
  <link rel="stylesheet" type="text/css" href="vendors/nprogress/nprogress.css">
  <link rel="stylesheet" type="text/css" href="vendors/summernote/dist/summernote.css">
  <link rel="stylesheet" type="text/css" href="vendors/dropify/dist/css/dropify.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/jquery-steps/demo/css/jquery.steps.css">
  <link rel="stylesheet" type="text/css" href="vendors/select2/dist/css/select2.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/bootstrap-select/dist/css/bootstrap-select.min.css">


  <script src="vendors/jquery/dist/jquery.min.js"></script>
  <script src="vendors/popper.js/dist/umd/popper.js"></script>
  <script src="vendors/bootstrap/dist/js/bootstrap.js"></script>
  <script src="vendors/jquery-mousewheel/jquery.mousewheel.min.js"></script>
  <script src="vendors/perfect-scrollbar/js/perfect-scrollbar.jquery.js"></script>
  <script src="vendors/chartist/dist/chartist.min.js"></script>
  <script src="vendors/chart.js/dist/Chart.min.js"></script>
  <script src="vendors/jqvmap/dist/jquery.vmap.min.js"></script>
  <script src="vendors/jqvmap/dist/maps/jquery.vmap.usa.js"></script>
  <script src="vendors/chartist-plugin-tooltip/dist/chartist-plugin-tooltip.min.js"></script>
  <script src="vendors/d3/d3.min.js"></script>
  <script src="vendors/d3-dsv/dist/d3-dsv.js"></script>
  <script src="vendors/d3-time-format/dist/d3-time-format.js"></script>
  <script src="vendors/c3/c3.min.js"></script>
  <script src="vendors/peity/jquery.peity.min.js"></script>
  <script type="text/javascript"
    src="../cdn.datatables.net/v/bs4/dt-1.10.18/fc-3.2.5/r-2.2.2/datatables.min.js"></script>
  <script src="vendors/editable-table/mindmup-editabletable.js"></script>
  <script src="vendors/moment/min/moment.min.js"></script>
  <script src="vendors/tempus-dominus-bs4/build/js/tempusdominus-bootstrap-4.min.js"></script>
  <script src="vendors/fullcalendar/dist/fullcalendar.min.js"></script>
  <script src="vendors/owl.carousel/dist/owl.carousel.min.js"></script>
  <script src="vendors/ionrangeslider/js/ion.rangeSlider.min.js"></script>
  <script src="vendors/remarkable-bootstrap-notify/dist/bootstrap-notify.min.js"></script>
  <script src="vendors/bootstrap-sweetalert/dist/sweetalert.min.js"></script>
  <script src="vendors/nprogress/nprogress.js"></script>
  <script src="vendors/summernote/dist/summernote.min.js"></script>
  <script src="vendors/nestable/jquery.nestable.js"></script>
  <script src="vendors/jquery-typeahead/dist/jquery.typeahead.min.js"></script>
  <script src="vendors/autosize/dist/autosize.min.js"></script>
  <script src="vendors/bootstrap-show-password/dist/bootstrap-show-password.min.js"></script>
  <script src="vendors/dropify/dist/js/dropify.min.js"></script>
  <script src="vendors/html5-form-validation/dist/jquery.validation.min.js"></script>
  <script src="vendors/jquery-steps/build/jquery.steps.min.js"></script>
  <script src="vendors/jquery-mask-plugin/dist/jquery.mask.min.js"></script>
  <script src="vendors/select2/dist/js/select2.full.min.js"></script>
  <script src="vendors/bootstrap-select/dist/js/bootstrap-select.min.js"></script>
  <script src="vendors/d3-dsv/dist/d3-dsv.js"></script>
  <script src="vendors/d3-time-format/dist/d3-time-format.js"></script>
  <script src="vendors/techan/dist/techan.min.js"></script>
  <script src="vendors/Stickyfill/dist/stickyfill.min.js"></script>

  <!-- AIR UI HTML ADMIN TEMPLATE MODULES-->
  <link rel="stylesheet" type="text/css" href="components/vendors/style.css">
  <link rel="stylesheet" type="text/css" href="components/core/style.css">
  <link rel="stylesheet" type="text/css" href="components/widgets/style.css">
  <link rel="stylesheet" type="text/css" href="components/system/style.css">
  <link rel="stylesheet" type="text/css" href="components/menu-left/style.css">
  <link rel="stylesheet" type="text/css" href="components/menu-top/style.css">
  <link rel="stylesheet" type="text/css" href="components/footer/style.css">
  <link rel="stylesheet" type="text/css" href="components/footer-dark/style.css">
  <link rel="stylesheet" type="text/css" href="components/topbar/style.css">
  <link rel="stylesheet" type="text/css" href="components/topbar-dark/style.css">
  <link rel="stylesheet" type="text/css" href="components/subbar/style.css">
  <link rel="stylesheet" type="text/css" href="components/sidebar/style.css">
  <link rel="stylesheet" type="text/css" href="components/chat/style.css">
  <link rel="stylesheet" type="text/css" href="components/apps/style.css">
  <link rel="stylesheet" type="text/css" href="components/apps/style.css">
  <link rel="stylesheet" type="text/css" href="components/extra-apps/style.css">
  <link rel="stylesheet" type="text/css" href="components/ecommerce/style.css">
  <link rel="stylesheet" type="text/css" href="components/dashboards/crypto-terminal/style.css">

  <script src="components/core/index.js"></script>
  <script src="components/menu-left/index.js"></script>
  <script src="components/menu-top/index.js"></script>
  <script src="components/sidebar/index.js"></script>
  <script src="components/topbar/index.js"></script>
  <script src="components/chat/index.js"></script>
  <!-- PRELOADER STYLES-->

</head>
<body class="air__menu--blue air__menu__submenu--blue">
  <div class="air__initialLoading"></div>
  <div class="air__layout">
    <div class="air__menuTop">
      <div class="air__menuTop__outer">
        <div class="air__menuTop__mobileToggleButton air__menuTop__mobileActionToggle">
          <span></span>
        </div>
       <a href="home.php" class="air__menuTop__logo">
          <h1 style="color:#FFF"><?php echo SITE; ?></h1>
        </a>
        <?php include('inc/__menu.php'); ?>
      </div>
    </div>
 <div class="air__menuTop__backdrop air__menuTop__mobileActionToggle"></div>
   <div class="air__layout">
      <?php include('inc/__header.php');?>
      <div class="air__layout__content">
        <div class="air__utils__content">
          <div class="air__utils__heading">
  <h5>Site Account: Setting</h5>
  <nav aria-label="breadcrumb" style="float: right;margin-top: -35px;">
          <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="home.php">Home</a></li>
            <li class="breadcrumb-item active" aria-current="page">Setting</li>
          </ol>
        </nav>
</div>
<?php

$sel=dbQuery("SELECT * FROM tabl_delivery_setting WHERE id='1'");
    $res=dbFetchAssoc($sel);
 ?>
<div class="row">
  <div class="col-xl-8 col-lg-12">
    <div class="card">
        <div class="card-body">
        <div class="tab-content">
          <div class="tab-pane fade show active" id="tab-settings-content" role="tabpanel" aria-labelledby="tab-settings-content">
            <h5 class="text-black mt-4">
              <strong>Site Information & Payment Settings</strong>
            </h5>
            <form name="setting" method="post" enctype="multipart/form-data">
            <div class="row">
                                                <div class="col-lg-11 mx-auto">
                                                    <div class="row">
                                                       <div class="col-xl-10 col-lg-12 col-md-8 mt-md-0 mt-4">
                                                            <div class="form">
                                                                <div class="row">

                                                                 <div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <label for="fullName">Site Name</label>
                                                                            <input type="text" class="form-control mb-4" id="Name" name="site_name" placeholder="Site Name" value="<?php echo $res['site_name'];?>" required>
                                                                        </div>

                                                                    </div>

                                                                    <div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <label for="fullName">Site Email</label>
                                                                            <input type="email" class="form-control mb-4" id="Name" name="site_email" placeholder="Site Email" value="<?php echo $res['site_email'];?>" required>
                                                                        </div>
                                                                    </div>

                                                                    <div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <label for="upi_id">UPI ID</label>
                                                                            <input type="text" class="form-control mb-4" id="upi_id" name="upi_id" placeholder="Enter UPI ID (e.g., yourname@paytm)" value="<?php echo $res['upi_id'];?>">
                                                                            <small class="form-text text-muted">Enter your UPI ID for manual payments</small>
                                                                        </div>
                                                                    </div>

                                                                    <div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <label for="qr_code">QR Code for Payments</label>
                                                                            <input type="file" class="form-control mb-4" id="qr_code" name="qr_code" accept="image/*">
                                                                            <small class="form-text text-muted">Upload QR code image for manual payments (JPG, PNG, GIF allowed)</small>

                                                                            <?php if(!empty($res['qr_code'])): ?>
                                                                            <div class="mt-3">
                                                                                <label>Current QR Code:</label><br>
                                                                                <img src="../assets/images/qr_code/<?php echo $res['qr_code']; ?>" alt="Current QR Code" style="max-width: 200px; max-height: 200px; border: 1px solid #ddd; padding: 10px; border-radius: 8px;">
                                                                            </div>
                                                                            <?php endif; ?>
                                                                        </div>
                                                                    </div>
                                                                  </div>
                                                             </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
            <div class="form-actions">
              <div class="form-group">
                <button type="submit" name="submit" class="btn width-200 btn-primary">Submit</button>
                <button type="reset" class="btn btn-default">Cancel</button>
              </div>
            </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
        </div>
      </div>
      <?php include('inc/__footer.php'); ?>
    </div>
  </div>

<script>
// QR Code image preview
document.getElementById('qr_code').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            // Remove existing preview if any
            const existingPreview = document.getElementById('qr_preview');
            if (existingPreview) {
                existingPreview.remove();
            }

            // Create new preview
            const preview = document.createElement('div');
            preview.id = 'qr_preview';
            preview.className = 'mt-3';
            preview.innerHTML = `
                <label>Preview:</label><br>
                <img src="${e.target.result}" alt="QR Code Preview" style="max-width: 200px; max-height: 200px; border: 1px solid #ddd; padding: 10px; border-radius: 8px;">
            `;

            // Insert preview after the file input
            e.target.parentNode.appendChild(preview);
        };
        reader.readAsDataURL(file);
    }
});

// Form validation
document.querySelector('form[name="setting"]').addEventListener('submit', function(e) {
    const upiId = document.getElementById('upi_id').value.trim();

    // Basic UPI ID validation
    if (upiId && !upiId.includes('@')) {
        alert('Please enter a valid UPI ID (e.g., yourname@paytm)');
        e.preventDefault();
        return false;
    }

    // File size validation for QR code
    const qrFile = document.getElementById('qr_code').files[0];
    if (qrFile) {
        const maxSize = 5 * 1024 * 1024; // 5MB
        if (qrFile.size > maxSize) {
            alert('QR code image size should be less than 5MB');
            e.preventDefault();
            return false;
        }
    }
});
</script>

</body>
</html>