/* BUTTONS */
.btn-group-justified {
    display: table;
    width: 100%;
    table-layout: fixed;
    border-collapse: separate;
}

.btn-group-justified > .btn-group {
    float: none;
    display: table-cell;
    width: 1%;
}

.btn-group-justified > .btn-group > .btn {
    width: 100%;
}

.btn-group-justified.btn-group-vertical {
    display: block;
}

.btn-group-justified.btn-group-vertical > .btn-group {
    display: block;
    width: 100%;
}

.btn-group-justified.btn-group-vertical > label {
    margin-bottom: 0;
}

label.btn input {
    display: none;
}

.btn {
    outline: none !important;
    -webkit-box-shadow: none !important;
            box-shadow: none !important;
    -webkit-transition: color 0.2s ease-in-out, background 0.2s ease-in-out, border 0.2s ease-in-out;
    -o-transition: color 0.2s ease-in-out, background 0.2s ease-in-out, border 0.2s ease-in-out;
    transition: color 0.2s ease-in-out, background 0.2s ease-in-out, border 0.2s ease-in-out;
}

.btn:hover, .btn:active {
    -webkit-transition: color 0.1s ease-in-out, background 0.1s ease-in-out, border 0.1s ease-in-out;
    -o-transition: color 0.1s ease-in-out, background 0.1s ease-in-out, border 0.1s ease-in-out;
    transition: color 0.1s ease-in-out, background 0.1s ease-in-out, border 0.1s ease-in-out;
}

.btn.btn-rounded {
    -webkit-border-radius: 100px;
            border-radius: 100px;
}

.btn.btn-squared {
    -webkit-border-radius: 0;
            border-radius: 0;
}

.btn.btn-link {
    color: #1b55e3;
    border-color: transparent !important;
    background: none !important;
    text-decoration: none;
}

.btn.btn-link:hover, .btn.btn-link:active, .btn.btn-link:focus, .btn.btn-link.active {
    color: #786fa4;
}

.btn-with-addon {
    overflow: hidden;
    position: relative;
    padding-left: 3.33rem !important;
    border: none;
}

.btn-with-addon .btn-addon {
    position: absolute;
    z-index: 1;
    top: -1px;
    left: -1px;
    bottom: -1px;
    background-color: rgba(255, 255, 255, 0.2);
    width: 2.66rem;
}

.btn-with-addon .btn-addon-icon {
    font-size: 1.06rem;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
        -ms-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
}

.btn,
.show > .btn {
    color: #786fa4;
    background-color: #fff;
    border-color: #e4e9f0;
}

.btn:hover, .btn:active,
.show > .btn:hover,
.show > .btn:active {
    background-color: #e4e9f0;
    border-color: #e4e9f0;
}

.btn:hover:active, .btn:focus, .btn.active,
.show > .btn:hover:active,
.show > .btn:focus,
.show > .btn.active {
    background-color: #e4e9f0;
    border-color: #e4e9f0;
}

.btn.btn-default, .btn.btn-primary, .btn.btn-secondary, .btn.btn-success, .btn.btn-info, .btn.btn-warning, .btn.btn-danger, .btn.btn-dark,
.show > .btn.btn-default,
.show > .btn.btn-primary,
.show > .btn.btn-secondary,
.show > .btn.btn-success,
.show > .btn.btn-info,
.show > .btn.btn-warning,
.show > .btn.btn-danger,
.show > .btn.btn-dark {
    color: #fff !important;
}

.btn.btn-default,
.show > .btn.btn-default {
    background-color: #c3bedc;
    border-color: #c3bedc;
}

.btn.btn-default:hover, .btn.btn-default:active,
.show > .btn.btn-default:hover,
.show > .btn.btn-default:active {
    background-color: #d5d2e7;
    border-color: #d5d2e7;
}

.btn.btn-default:hover:active, .btn.btn-default:focus, .btn.btn-default.active,
.show > .btn.btn-default:hover:active,
.show > .btn.btn-default:focus,
.show > .btn.btn-default.active {
    background: #b1aad1;
    border-color: #b1aad1;
}

.btn.btn-primary,
.show > .btn.btn-primary {
    background-color: #1b55e3;
    border-color: #1b55e3;
}

.btn.btn-primary:hover, .btn.btn-primary:active,
.show > .btn.btn-primary:hover,
.show > .btn.btn-primary:active {
    background-color: #3669e7;
    border-color: #3669e7;
}

.btn.btn-primary:hover:active, .btn.btn-primary:focus, .btn.btn-primary.active,
.show > .btn.btn-primary:hover:active,
.show > .btn.btn-primary:focus,
.show > .btn.btn-primary.active {
    background: #184bc8 !important;
    border-color: #184bc8 !important;
}

.btn.btn-secondary,
.show > .btn.btn-secondary {
    background-color: #6a7a84;
    border-color: #6a7a84;
}

.btn.btn-secondary:hover, .btn.btn-secondary:active,
.show > .btn.btn-secondary:hover,
.show > .btn.btn-secondary:active {
    background-color: #798993;
    border-color: #798993;
}

.btn.btn-secondary:hover:active, .btn.btn-secondary:focus, .btn.btn-secondary.active,
.show > .btn.btn-secondary:hover:active,
.show > .btn.btn-secondary:focus,
.show > .btn.btn-secondary.active {
    background: #5c6a73;
    border-color: #5c6a73;
}

.btn.btn-success,
.show > .btn.btn-success {
    background-color: #46be8a;
    border-color: #46be8a;
}

.btn.btn-success:hover, .btn.btn-success:active,
.show > .btn.btn-success:hover,
.show > .btn.btn-success:active {
    background-color: #5dc698;
    border-color: #5dc698;
}

.btn.btn-success:hover:active, .btn.btn-success:focus, .btn.btn-success.active,
.show > .btn.btn-success:hover:active,
.show > .btn.btn-success:focus,
.show > .btn.btn-success.active {
    background: #3caa7a;
    border-color: #3caa7a;
}

.btn.btn-danger,
.show > .btn.btn-danger {
    background-color: #fb434a;
    border-color: #fb434a;
}

.btn.btn-danger:hover, .btn.btn-danger:active,
.show > .btn.btn-danger:hover,
.show > .btn.btn-danger:active {
    background-color: #fc6167;
    border-color: #fc6167;
}

.btn.btn-danger:hover:active, .btn.btn-danger:focus, .btn.btn-danger.active,
.show > .btn.btn-danger:hover:active,
.show > .btn.btn-danger:focus,
.show > .btn.btn-danger.active {
    background: #fa252d;
    border-color: #fa252d;
}

.btn.btn-warning,
.show > .btn.btn-warning {
    background-color: #f39834;
    border-color: #f39834;
}

.btn.btn-warning:hover, .btn.btn-warning:active,
.show > .btn.btn-warning:hover,
.show > .btn.btn-warning:active {
    background-color: #f5a751;
    border-color: #f5a751;
}

.btn.btn-warning:hover:active, .btn.btn-warning:focus, .btn.btn-warning.active,
.show > .btn.btn-warning:hover:active,
.show > .btn.btn-warning:focus,
.show > .btn.btn-warning.active {
    background: #f18917;
    border-color: #f18917;
}

.btn.btn-info,
.show > .btn.btn-info {
    background-color: #0887c9;
    border-color: #0887c9;
}

.btn.btn-info:hover, .btn.btn-info:active,
.show > .btn.btn-info:hover,
.show > .btn.btn-info:active {
    background-color: #099be6;
    border-color: #099be6;
}

.btn.btn-info:hover:active, .btn.btn-info:focus, .btn.btn-info.active,
.show > .btn.btn-info:hover:active,
.show > .btn.btn-info:focus,
.show > .btn.btn-info.active {
    background: #0773ac;
    border-color: #0773ac;
}

.btn.btn-light,
.show > .btn.btn-light {
    background-color: #f2f4f8;
    border-color: #f2f4f8;
}

.btn.btn-light:hover, .btn.btn-light:active,
.show > .btn.btn-light:hover,
.show > .btn.btn-light:active {
    background-color: #dee3ed;
    border-color: #dee3ed;
}

.btn.btn-light:hover:active, .btn.btn-light:focus, .btn.btn-light.active,
.show > .btn.btn-light:hover:active,
.show > .btn.btn-light:focus,
.show > .btn.btn-light.active {
    background: #d1d8e6;
    border-color: #d1d8e6;
}

.btn.btn-dark,
.show > .btn.btn-dark {
    background-color: #161537;
    border-color: #161537;
}

.btn.btn-dark:hover, .btn.btn-dark:active,
.show > .btn.btn-dark:hover,
.show > .btn.btn-dark:active {
    background-color: #1f1d4d;
    border-color: #1f1d4d;
}

.btn.btn-dark:hover:active, .btn.btn-dark:focus, .btn.btn-dark.active,
.show > .btn.btn-dark:hover:active,
.show > .btn.btn-dark:focus,
.show > .btn.btn-dark.active {
    background: #0d0d21;
    border-color: #0d0d21;
}

.btn.btn-outline-default,
.show > .btn.btn-outline-default {
    border-color: #c3bedc;
    color: #c3bedc;
}

.btn.btn-outline-default:hover, .btn.btn-outline-default:active,
.show > .btn.btn-outline-default:hover,
.show > .btn.btn-outline-default:active {
    background-color: #d5d2e7;
    border-color: #d5d2e7;
    color: #fff;
}

.btn.btn-outline-default:hover:active, .btn.btn-outline-default:focus, .btn.btn-outline-default.active,
.show > .btn.btn-outline-default:hover:active,
.show > .btn.btn-outline-default:focus,
.show > .btn.btn-outline-default.active {
    background: #b1aad1;
    border-color: #b1aad1;
    color: #fff;
}

.btn.btn-outline-primary,
.show > .btn.btn-outline-primary {
    border-color: #1b55e3;
    color: #1b55e3;
}

.btn.btn-outline-primary:hover, .btn.btn-outline-primary:active,
.show > .btn.btn-outline-primary:hover,
.show > .btn.btn-outline-primary:active {
    background-color: #3669e7;
    border-color: #3669e7;
    color: #fff;
}

.btn.btn-outline-primary:hover:active, .btn.btn-outline-primary:focus, .btn.btn-outline-primary.active,
.show > .btn.btn-outline-primary:hover:active,
.show > .btn.btn-outline-primary:focus,
.show > .btn.btn-outline-primary.active {
    background: #184bc8;
    border-color: #184bc8;
    color: #fff;
}

.btn.btn-outline-secondary,
.show > .btn.btn-outline-secondary {
    border-color: #6a7a84;
    color: #6a7a84;
}

.btn.btn-outline-secondary:hover, .btn.btn-outline-secondary:active,
.show > .btn.btn-outline-secondary:hover,
.show > .btn.btn-outline-secondary:active {
    background-color: #798993;
    border-color: #798993;
    color: #fff;
}

.btn.btn-outline-secondary:hover:active, .btn.btn-outline-secondary:focus, .btn.btn-outline-secondary.active,
.show > .btn.btn-outline-secondary:hover:active,
.show > .btn.btn-outline-secondary:focus,
.show > .btn.btn-outline-secondary.active {
    background: #5c6a73;
    border-color: #5c6a73;
    color: #fff;
}

.btn.btn-outline-success,
.show > .btn.btn-outline-success {
    border-color: #46be8a;
    color: #46be8a;
}

.btn.btn-outline-success:hover, .btn.btn-outline-success:active,
.show > .btn.btn-outline-success:hover,
.show > .btn.btn-outline-success:active {
    background-color: #5dc698;
    border-color: #5dc698;
    color: #fff;
}

.btn.btn-outline-success:hover:active, .btn.btn-outline-success:focus, .btn.btn-outline-success.active,
.show > .btn.btn-outline-success:hover:active,
.show > .btn.btn-outline-success:focus,
.show > .btn.btn-outline-success.active {
    background: #3caa7a;
    border-color: #3caa7a;
    color: #fff;
}

.btn.btn-outline-danger,
.show > .btn.btn-outline-danger {
    border-color: #fb434a;
    color: #fb434a;
}

.btn.btn-outline-danger:hover, .btn.btn-outline-danger:active,
.show > .btn.btn-outline-danger:hover,
.show > .btn.btn-outline-danger:active {
    background-color: #fc6167;
    border-color: #fc6167;
    color: #fff;
}

.btn.btn-outline-danger:hover:active, .btn.btn-outline-danger:focus, .btn.btn-outline-danger.active,
.show > .btn.btn-outline-danger:hover:active,
.show > .btn.btn-outline-danger:focus,
.show > .btn.btn-outline-danger.active {
    background: #fa252d;
    border-color: #fa252d;
    color: #fff;
}

.btn.btn-outline-warning,
.show > .btn.btn-outline-warning {
    border-color: #f39834;
    color: #f39834;
}

.btn.btn-outline-warning:hover, .btn.btn-outline-warning:active,
.show > .btn.btn-outline-warning:hover,
.show > .btn.btn-outline-warning:active {
    background-color: #f5a751;
    border-color: #f5a751;
    color: #fff;
}

.btn.btn-outline-warning:hover:active, .btn.btn-outline-warning:focus, .btn.btn-outline-warning.active,
.show > .btn.btn-outline-warning:hover:active,
.show > .btn.btn-outline-warning:focus,
.show > .btn.btn-outline-warning.active {
    background: #f18917;
    border-color: #f18917;
    color: #fff;
}

.btn.btn-outline-info,
.show > .btn.btn-outline-info {
    border-color: #0887c9;
    color: #0887c9;
}

.btn.btn-outline-info:hover, .btn.btn-outline-info:active,
.show > .btn.btn-outline-info:hover,
.show > .btn.btn-outline-info:active {
    background-color: #099be6;
    border-color: #099be6;
    color: #fff;
}

.btn.btn-outline-info:hover:active, .btn.btn-outline-info:focus, .btn.btn-outline-info.active,
.show > .btn.btn-outline-info:hover:active,
.show > .btn.btn-outline-info:focus,
.show > .btn.btn-outline-info.active {
    background: #0773ac;
    border-color: #0773ac;
    color: #fff;
}

.btn.btn-outline-light,
.show > .btn.btn-outline-light {
    border-color: #f2f4f8;
    color: #786fa4;
}

.btn.btn-outline-light:hover, .btn.btn-outline-light:active,
.show > .btn.btn-outline-light:hover,
.show > .btn.btn-outline-light:active {
    background-color: #dee3ed;
    border-color: #dee3ed;
    color: #1b55e3;
}

.btn.btn-outline-light:hover:active, .btn.btn-outline-light:focus, .btn.btn-outline-light.active,
.show > .btn.btn-outline-light:hover:active,
.show > .btn.btn-outline-light:focus,
.show > .btn.btn-outline-light.active {
    background: #dee3ed;
    border-color: #dee3ed;
    color: #1b55e3;
}

.btn.btn-outline-dark,
.show > .btn.btn-outline-dark {
    border-color: #161537;
    color: #161537;
}

.btn.btn-outline-dark:hover, .btn.btn-outline-dark:active,
.show > .btn.btn-outline-dark:hover,
.show > .btn.btn-outline-dark:active {
    background-color: #1f1d4d;
    border-color: #1f1d4d;
    color: #fff;
}

.btn.btn-outline-dark:hover:active, .btn.btn-outline-dark:focus, .btn.btn-outline-dark.active,
.show > .btn.btn-outline-dark:hover:active,
.show > .btn.btn-outline-dark:focus,
.show > .btn.btn-outline-dark.active {
    background: #0d0d21;
    border-color: #0d0d21;
    color: #fff;
}
