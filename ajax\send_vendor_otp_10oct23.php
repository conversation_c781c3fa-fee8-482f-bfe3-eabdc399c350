<?php 

session_start();

include('../admin/lib/db_connection.php');

date_default_timezone_set("Asia/Kolkata");

$date=date('Y-m-d H:i:s');



$otp=rand(1000,9999);

$sel_email=dbQuery("SELECT * FROM tabl_vendor WHERE email='".$_REQUEST['email']."'");

$num1=dbNumRows($sel_email);

if($num1>0){

	echo 2;

	die();

}else{

	

$sel_phone=dbQuery("SELECT * FROM tabl_vendor WHERE phone='".$_REQUEST['phone']."'");

$num2=dbNumRows($sel_phone);

if($num2>0){

	echo 3;

	die();	

}else{



dbQuery("DELETE FROM tabl_vendor_otp WHERE email='".$_REQUEST['email']."'");

dbQuery("DELETE FROM tabl_temp_vendor WHERE email='".$_REQUEST['email']."'");



dbQuery("INSERT INTO tabl_vendor_otp SET otp='".$otp."',email='".$_REQUEST['email']."'");



dbQuery("INSERT INTO tabl_temp_vendor SET name='".mysqli_real_escape_string($con,$_REQUEST['name'])."',phone='".$_REQUEST['phone']."',email='".$_REQUEST['email']."',password='".md5($_REQUEST['password'])."',date_added='".$date."'");

$to = $_REQUEST['email'];
$subject = 'Vendor Login OTP | Project Womaniya Shopping';
$message ='<html>
<body>
<h1>Project Womaniya Shopping</h1> 
<div class="text" style="padding: 0 3em;">
<p>Here is your otp for login:</p> 
<h2>'.$otp.'</h2>
<p>Regards<br/>
Team Project Womaniya</p>
</div>
</html>';
$headers[] = 'MIME-Version: 1.0';
$headers[] = 'Content-type: text/html; charset=iso-8859-1';
$headers[] = 'From: '.SITE.' <'.EMAIL.'>';
mail($to, $subject, $message, implode("\r\n", $headers));

$_SESSION['vendor_login_email']=$_REQUEST['email'];

 echo 1;		

  }

}

?>