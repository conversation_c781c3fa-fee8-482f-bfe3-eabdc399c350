<?php
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page = 4;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');

$sel = dbQuery("SELECT tabl_delivery_vendor.*, tabl_city.city, tabl_state.state_name
             FROM tabl_delivery_vendor
             LEFT JOIN tabl_city ON tabl_delivery_vendor.city_id = tabl_city.id
             LEFT JOIN tabl_state ON tabl_city.state_id = tabl_state.id
             WHERE tabl_delivery_vendor.id='" . $_REQUEST['id'] . "'");
$res = dbFetchAssoc($sel);

if (isset($_REQUEST['submit'])) {

  $sel = dbQuery("SELECT * FROM tabl_delivery_vendor WHERE phone='" . $_REQUEST['phone'] . "' AND id!='" . $_REQUEST['id'] . "'");
  $num_phone = dbNumRows($sel);
  if ($num_phone > 0) {
    echo '<script>alert("Phone no. already exist.");window.location.href="edit_vendor.php?id=' . $_REQUEST['id'] . '";</script>';
  } else {
    $sel1 = dbQuery("SELECT * FROM tabl_delivery_vendor WHERE email='" . $_REQUEST['email'] . "' AND id!='" . $_REQUEST['id'] . "'");
    $num_email = dbNumRows($sel1);
    if ($num_email > 0) {
      echo '<script>alert("Email address already exist.");window.location.href="edit_vendor.php?id=' . $_REQUEST['id'] . '";</script>';
    } else {
      dbQuery("UPDATE tabl_delivery_vendor SET name='" . mysqli_real_escape_string($con, $_REQUEST['name']) . "',email='" . $_REQUEST['email'] . "',phone='" . $_REQUEST['phone'] . "',city_id='" . $_REQUEST['city_id'] . "',status='" . $_REQUEST['status'] . "',turn_over='" . $_REQUEST['turn_over'] . "' WHERE  id='" . $_REQUEST['id'] . "'");
      echo '<script>alert("Vendor Update successfully.");window.location.href="vendors.php?id=' . $_REQUEST['id'] . '";</script>';
    }
  }
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <title><?php echo SITE; ?>| Edit Vendor</title>
  <link href="favicon.png" rel="shortcut icon">
  <link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:400,400i,700,700i,900" rel="stylesheet">
  <!-- VENDORS -->
  <link rel="stylesheet" type="text/css" href="vendors/bootstrap/dist/css/bootstrap.css">
  <link rel="stylesheet" type="text/css" href="vendors/font-feathericons/dist/feather.css">
  <link rel="stylesheet" type="text/css" href="vendors/font-awesome/css/font-awesome.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/font-linearicons/style.css">
  <link rel="stylesheet" type="text/css" href="vendors/font-icomoon/style.css">
  <link rel="stylesheet" type="text/css" href="vendors/perfect-scrollbar/css/perfect-scrollbar.css">
  <link rel="stylesheet" type="text/css" href="vendors/chart.js/dist/Chart.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/jqvmap/dist/jqvmap.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/c3/c3.min.css">
  <link rel="stylesheet" type="text/css"
    href="cdn.datatables.net/v/bs4/dt-1.10.18/fc-3.2.5/r-2.2.2/datatables.min.css" />
  <link rel="stylesheet" type="text/css" href="vendors/tempus-dominus-bs4/build/css/tempusdominus-bootstrap-4.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/fullcalendar/dist/fullcalendar.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/owl.carousel/dist/assets/owl.carousel.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/ionrangeslider/css/ion.rangeSlider.css">
  <link rel="stylesheet" type="text/css" href="vendors/bootstrap-sweetalert/dist/sweetalert.css">
  <link rel="stylesheet" type="text/css" href="vendors/nprogress/nprogress.css">
  <link rel="stylesheet" type="text/css" href="vendors/summernote/dist/summernote.css">
  <link rel="stylesheet" type="text/css" href="vendors/dropify/dist/css/dropify.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/jquery-steps/demo/css/jquery.steps.css">
  <link rel="stylesheet" type="text/css" href="vendors/select2/dist/css/select2.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/bootstrap-select/dist/css/bootstrap-select.min.css">
  <script src="vendors/jquery/dist/jquery.min.js"></script>
  <script src="vendors/popper.js/dist/umd/popper.js"></script>
  <script src="vendors/bootstrap/dist/js/bootstrap.js"></script>
  <script src="vendors/jquery-mousewheel/jquery.mousewheel.min.js"></script>
  <script src="vendors/perfect-scrollbar/js/perfect-scrollbar.jquery.js"></script>
  <script src="vendors/chartist/dist/chartist.min.js"></script>
  <script src="vendors/chart.js/dist/Chart.min.js"></script>
  <script src="vendors/jqvmap/dist/jquery.vmap.min.js"></script>
  <script src="vendors/jqvmap/dist/maps/jquery.vmap.usa.js"></script>
  <script src="vendors/chartist-plugin-tooltip/dist/chartist-plugin-tooltip.min.js"></script>
  <script src="vendors/d3/d3.min.js"></script>
  <script src="vendors/d3-dsv/dist/d3-dsv.js"></script>
  <script src="vendors/d3-time-format/dist/d3-time-format.js"></script>
  <script src="vendors/c3/c3.min.js"></script>
  <script src="vendors/peity/jquery.peity.min.js"></script>
  <script type="text/javascript" src="cdn.datatables.net/v/bs4/dt-1.10.18/fc-3.2.5/r-2.2.2/datatables.min.js"></script>
  <script src="vendors/editable-table/mindmup-editabletable.js"></script>
  <script src="vendors/moment/min/moment.min.js"></script>
  <script src="vendors/tempus-dominus-bs4/build/js/tempusdominus-bootstrap-4.min.js"></script>
  <script src="vendors/fullcalendar/dist/fullcalendar.min.js"></script>
  <script src="vendors/owl.carousel/dist/owl.carousel.min.js"></script>
  <script src="vendors/ionrangeslider/js/ion.rangeSlider.min.js"></script>
  <script src="vendors/remarkable-bootstrap-notify/dist/bootstrap-notify.min.js"></script>
  <script src="vendors/bootstrap-sweetalert/dist/sweetalert.min.js"></script>
  <script src="vendors/nprogress/nprogress.js"></script>
  <script src="vendors/summernote/dist/summernote.min.js"></script>
  <script src="vendors/nestable/jquery.nestable.js"></script>
  <script src="vendors/jquery-typeahead/dist/jquery.typeahead.min.js"></script>
  <script src="vendors/autosize/dist/autosize.min.js"></script>
  <script src="vendors/bootstrap-show-password/dist/bootstrap-show-password.min.js"></script>
  <script src="vendors/dropify/dist/js/dropify.min.js"></script>
  <script src="vendors/html5-form-validation/dist/jquery.validation.min.js"></script>
  <script src="vendors/jquery-steps/build/jquery.steps.min.js"></script>
  <script src="vendors/jquery-mask-plugin/dist/jquery.mask.min.js"></script>
  <script src="vendors/select2/dist/js/select2.full.min.js"></script>
  <script src="vendors/bootstrap-select/dist/js/bootstrap-select.min.js"></script>
  <script src="vendors/d3-dsv/dist/d3-dsv.js"></script>
  <script src="vendors/d3-time-format/dist/d3-time-format.js"></script>
  <script src="vendors/techan/dist/techan.min.js"></script>
  <script src="vendors/Stickyfill/dist/stickyfill.min.js"></script>

  <!-- AIR UI HTML ADMIN TEMPLATE MODULES-->
  <link rel="stylesheet" type="text/css" href="components/vendors/style.css">
  <link rel="stylesheet" type="text/css" href="components/core/style.css">
  <link rel="stylesheet" type="text/css" href="components/widgets/style.css">
  <link rel="stylesheet" type="text/css" href="components/system/style.css">
  <link rel="stylesheet" type="text/css" href="components/menu-left/style.css">
  <link rel="stylesheet" type="text/css" href="components/menu-top/style.css">
  <link rel="stylesheet" type="text/css" href="components/footer/style.css">
  <link rel="stylesheet" type="text/css" href="components/footer-dark/style.css">
  <link rel="stylesheet" type="text/css" href="components/topbar/style.css">
  <link rel="stylesheet" type="text/css" href="components/topbar-dark/style.css">
  <link rel="stylesheet" type="text/css" href="components/subbar/style.css">
  <link rel="stylesheet" type="text/css" href="components/sidebar/style.css">
  <link rel="stylesheet" type="text/css" href="components/chat/style.css">
  <link rel="stylesheet" type="text/css" href="components/apps/style.css">
  <link rel="stylesheet" type="text/css" href="components/apps/style.css">
  <link rel="stylesheet" type="text/css" href="components/extra-apps/style.css">
  <link rel="stylesheet" type="text/css" href="components/ecommerce/style.css">
  <link rel="stylesheet" type="text/css" href="components/dashboards/crypto-terminal/style.css">
  <script src="components/core/index.js"></script>
  <script src="components/menu-left/index.js"></script>
  <script src="components/menu-top/index.js"></script>
  <script src="components/sidebar/index.js"></script>
  <script src="components/topbar/index.js"></script>
  <script src="components/chat/index.js"></script>

  <!-- PRELOADER STYLES-->

  <style>
    .city-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border: 1px solid #ddd;
      border-radius: 8px;
      max-height: 200px;
      overflow-y: auto;
      z-index: 1000;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .city-dropdown-item {
      padding: 12px 16px;
      cursor: pointer;
      border-bottom: 1px solid #f0f0f0;
      transition: background-color 0.2s;
    }

    .city-dropdown-item:hover {
      background-color: #f8f9fa;
    }

    .city-dropdown-item:last-child {
      border-bottom: none;
    }

    .city-dropdown-item.selected {
      background-color: #007bff;
      color: white;
    }

    .form-group {
      position: relative;
    }

    .no-results {
      padding: 12px 16px;
      color: #6c757d;
      font-style: italic;
    }
  </style>

</head>

<body class="air__menu--blue air__menu__submenu--blue">
  <div class="air__initialLoading"></div>
  <div class="air__layout">
    <div class="air__menuTop">
      <div class="air__menuTop__outer">
        <div class="air__menuTop__mobileToggleButton air__menuTop__mobileActionToggle"> <span></span> </div>
        <a href="home.php" class="air__menuTop__logo">
          <h1 style="color:#FFF"><?php echo SITE; ?></h1>
        </a>
        <?php include('inc/__menu.php'); ?>
      </div>
    </div>
    <div class="air__menuTop__backdrop air__menuTop__mobileActionToggle"></div>
    <div class="air__layout">
      <?php include('inc/__header.php'); ?>
      <div class="air__layout__content">
        <div class="air__utils__content">
          <div class="air__utils__heading">
            <h5>Vendor: Edit Vendor</h5>
            <nav aria-label="breadcrumb" style="float: right;margin-top: -35px;">
              <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="home.php">Home</a></li>
                <li class="breadcrumb-item"><a href="vendors.php">Vendors</a></li>
                <li class="breadcrumb-item active" aria-current="page">Edit Vendor</li>
              </ol>
            </nav>
          </div>
          <div class="row">
            <div class="col-lg-12">
              <div class="card">
                <div class="card-body" style="background: aliceblue;">
                  <form method="POST" enctype="multipart/form-data">
                    <div class="row">
                      <div class="col-lg-6">
                        <div class="form-group">
                          <label class="form-label">Name</label>
                          <input class="form-control" name="name" id="name" value="<?php echo  $res['name']; ?>" type="text" required="">
                        </div>
                      </div>
                      <div class="col-lg-6">
                        <div class="form-group">
                          <label class="form-label">Email</label>
                          <input class="form-control" name="email" id="email" value="<?php echo  $res['email']; ?>" type="text" required="">
                        </div>
                      </div>
                      <div class="col-lg-6">
                        <div class="form-group">
                          <label class="form-label">Phone</label>
                          <input class="form-control" name="phone" id="phone" value="<?php echo  $res['phone']; ?>" onkeypress="return isNumber(event,this)" type="text" required="">
                        </div>
                      </div>

                      <div class="col-lg-6">
                        <div class="form-group">
                          <label class="form-label">City</label>
                          <input type="text" name="city_search" id="city_search" class="form-control" placeholder="Search and select city" autocomplete="off" value="<?php echo !empty($res['city']) ? $res['city'] : ''; ?>" required>
                          <input type="hidden" name="city_id" id="city_id" value="<?php echo $res['city_id']; ?>" required>
                          <div id="city_dropdown" class="city-dropdown" style="display: none;"></div>
                        </div>
                      </div>
                      <div class="col-lg-6">
                        <div class="form-group">
                          <label class="form-label">Status</label>
                          <select name="status" class="form-control">
                            <option value="0" <?php if ($res['status'] == 0) {
                                                echo 'selected';
                                              } else {
                                                echo '';
                                              } ?>>Disable</option>
                            <option value="1" <?php if ($res['status'] == 1) {
                                                echo 'selected';
                                              } else {
                                                echo '';
                                              } ?>>Active</option>
                          </select>
                        </div>
                      </div>

                      <div class="col-lg-12">
                        <div class="form-group">
                          <label class="form-label">Turnover(%)</label>
                          <input class="form-control" name="turn_over" id="turn_over" value="<?php echo  $res['turn_over']; ?>" onkeypress="return isDecimal(event,this)" type="text" required="">
                        </div>
                      </div>
                    </div>
                    <div class="col-lg-12">
                      <div class="form-group">
                        <label for="fullName"></label>
                        <button type="submit" name="submit" class="btn btn-secondary mb-4 mr-2">Submit</button>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <?php include('inc/__footer.php'); ?>
    </div>
  </div>
</body>

</html>
<script>
  ;
  (function($) {
    'use strict'
    $(function() {
      $('#example1').DataTable({
        responsive: true,
      })

      $('#example2').DataTable({
        autoWidth: true,
        scrollX: true,
        fixedColumns: true,
      })

      $('#example3').DataTable({
        autoWidth: true,
        scrollX: true,

        fixedColumns: true,
      })
    })
  })(jQuery)
</script>
<script>
  ;
  (function($) {
    'use strict'
    $(function() {
      $('#form-validation').validate({
        submit: {
          settings: {
            inputContainer: '.form-group',
            errorListClass: 'form-control-error',
            errorClass: 'has-danger',
          },
        },
      })

      $('#form-validation .remove-error').on('click', function() {
        $('#form-validation').removeError()
      })

      $('#form-validation-simple').validate({
        submit: {
          settings: {
            inputContainer: '.form-group',
            errorListClass: 'form-control-error-list',
            errorClass: 'has-danger',
          },
        },
      })

      $('#form-validation-simple .remove-error').on('click', function() {
        $('#form-validation-simple').removeError()
      })

      $('.select2').select2()
    })
  })(jQuery)
</script>
<script>
  function isNumber(evt) {
    var iKeyCode = (evt.which) ? evt.which : evt.keyCode
    if (iKeyCode != 46 && iKeyCode > 31 && (iKeyCode < 48 || iKeyCode > 57))
      return false;

    return true;
  }

  function isDecimal(evt, obj) {

    var charCode = (evt.which) ? evt.which : event.keyCode
    var value = obj.value;
    var dotcontains = value.indexOf(".") != -1;
    if (dotcontains)
      if (charCode == 46) return false;
    if (charCode == 46) return true;
    if (charCode > 31 && (charCode < 48 || charCode > 57))
      return false;
    return true;
  }
</script>

<script>
  function delete_user(id) {
    var retVal = confirm("Are you sure want to delete.");
    if (retVal == true) {
      $.ajax({
        url: 'ajax/delete_user.php',
        type: 'post',
        data: {
          'id': id
        },
        success: function(data) {
          //alert(data);
          if (data == 1) {
            location.reload();
          }
        },
      });
    } else {
      return false;
    }

  }
</script>
<script>
  function change_status(tabl, val, row_id) {
    var retVal = confirm("Are you sure want to change status.");
    if (retVal == true) {
      $.ajax({
        url: 'ajax/activate.php',
        type: 'post',
        data: {
          'tabl': tabl,
          'val': val,
          'row_id': row_id
        },
        success: function(data) {
          //alert(data);
          if (data == 1) {
            location.reload();
          }
        },
      });
    } else {
      return false;
    }


  }
</script>

<script>
  // City search functionality
  let searchTimeout;

  $("#city_search").on('input', function() {
    const searchTerm = $(this).val().trim();

    if (searchTerm.length < 2) {
      $("#city_dropdown").hide();
      return;
    }

    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(function() {
      searchCities(searchTerm);
    }, 300);
  });

  function searchCities(searchTerm) {
    $.ajax({
      url: 'ajax/search_cities.php',
      type: 'post',
      data: {
        search: searchTerm
      },
      success: function(data) {
        try {
          const cities = JSON.parse(data);
          displayCityDropdown(cities);
        } catch (e) {
          console.error('JSON Parse Error:', e);
          $("#city_dropdown").hide();
        }
      },
      error: function(xhr, status, error) {
        console.error('AJAX Error:', error);
        $("#city_dropdown").hide();
      }
    });
  }

  function displayCityDropdown(cities) {
    const dropdown = $("#city_dropdown");
    dropdown.empty();

    if (cities.length === 0) {
      dropdown.html('<div class="no-results">No cities found</div>');
    } else {
      cities.forEach(function(city) {
        const item = $('<div class="city-dropdown-item" data-id="' + city.id + '" data-name="' + city.city + '">' +
          city.city + ', ' + city.state_name + '</div>');
        dropdown.append(item);
      });
    }

    dropdown.show();
  }

  // Handle city selection
  $(document).on('click', '.city-dropdown-item', function() {
    const cityId = $(this).data('id');
    const cityName = $(this).data('name');

    $("#city_search").val(cityName);
    $("#city_id").val(cityId);
    $("#city_dropdown").hide();
  });

  // Hide dropdown when clicking outside
  $(document).on('click', function(e) {
    if (!$(e.target).closest('.form-group').length) {
      $("#city_dropdown").hide();
    }
  });

  // Form validation
  $('form').on('submit', function(e) {
    if (!$("#city_id").val()) {
      alert('Please select a city from the dropdown.');
      e.preventDefault();
      return false;
    }
  });
</script>

</body>

</html>