<?php
define('HOSTNAME', 'localhost');
// define ('USERNAME', 'boompass_new_boompass');
// define ('PASSWORD', 'X4B4pHucA5#X');
// define ('DATABASE_NAME', 'boompass_new_boompass');

// define ('USERNAME', 'root');
// define ('PASSWORD', '');
// define ('DATABASE_NAME', 'kri_boompass_new');

define('USERNAME', 'root');
define('PASSWORD', '');
define('DATABASE_NAME', 'kri_projectwomaniya2');


$con = mysqli_connect(HOSTNAME, USERNAME, PASSWORD, DATABASE_NAME);
if (mysqli_connect_errno()) {
	echo "Failed to connect to MySQL: " . mysqli_connect_error();
}

function dbQuery($sql)
{
	global $con;

	$result = mysqli_query($con, $sql);

	return $result;
}

function dbAffectedRows()
{
	global $con;

	return mysqli_affected_rows($con);
}

function dbFetchArray($result, $resultType = MYSQL_NUM)
{
	return mysqli_fetch_array($result, $resultType);
}

function dbFetchAssoc($result)
{
	return mysqli_fetch_assoc($result);
}

function dbFetchRow($result)
{
	return mysqli_fetch_row($result);
}

function dbFreeResult($result)
{
	return mysqli_free_result($result);
}

function dbNumRows($result)
{
	return mysqli_num_rows($result);
}

function dbSelect($con)
{
	return mysqli_select_db($con);
}

function dbInsertId()
{
	global $con;
	return mysqli_insert_id($con);
}

$setting = dbQuery("SELECT * FROM tabl_delivery_setting WHERE id=1");
$res_setting = dbFetchAssoc($setting);

define("SITE", $res_setting['site_name']);
define("EMAIL", $res_setting['site_email']);
