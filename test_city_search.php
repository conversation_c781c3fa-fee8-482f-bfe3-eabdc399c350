<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test City Search</title>
    <script src="assets/js/jquery.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
            position: relative;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        
        .city-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .city-dropdown-item {
            padding: 12px 16px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s;
        }
        
        .city-dropdown-item:hover {
            background-color: #f8f9fa;
        }
        
        .city-dropdown-item:last-child {
            border-bottom: none;
        }
        
        .no-results {
            padding: 12px 16px;
            color: #6c757d;
            font-style: italic;
        }
        
        .selected-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Test City Search Functionality</h1>
    
    <div class="form-group">
        <label for="city_search">Search City:</label>
        <input type="text" id="city_search" class="form-control" placeholder="Type to search cities..." autocomplete="off">
        <input type="hidden" id="city_id">
        <div id="city_dropdown" class="city-dropdown" style="display: none;"></div>
    </div>
    
    <div id="selected_info" class="selected-info" style="display: none;">
        <h3>Selected City:</h3>
        <p><strong>City ID:</strong> <span id="selected_id"></span></p>
        <p><strong>City Name:</strong> <span id="selected_name"></span></p>
    </div>

    <script>
        let searchTimeout;
        
        $("#city_search").on('input', function() {
            const searchTerm = $(this).val().trim();
            
            if (searchTerm.length < 2) {
                $("#city_dropdown").hide();
                return;
            }
            
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                searchCities(searchTerm);
            }, 300);
        });
        
        function searchCities(searchTerm) {
            $.ajax({
                url: 'ajax/search_cities.php',
                type: 'post',
                data: { search: searchTerm },
                success: function(data) {
                    console.log('Response:', data);
                    try {
                        const cities = JSON.parse(data);
                        displayCityDropdown(cities);
                    } catch(e) {
                        console.error('JSON Parse Error:', e);
                        $("#city_dropdown").hide();
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', error);
                    $("#city_dropdown").hide();
                }
            });
        }
        
        function displayCityDropdown(cities) {
            const dropdown = $("#city_dropdown");
            dropdown.empty();
            
            if (cities.length === 0) {
                dropdown.html('<div class="no-results">No cities found</div>');
            } else {
                cities.forEach(function(city) {
                    const item = $('<div class="city-dropdown-item" data-id="' + city.id + '" data-name="' + city.city + '">' + 
                                 city.city + ', ' + city.state_name + '</div>');
                    dropdown.append(item);
                });
            }
            
            dropdown.show();
        }
        
        // Handle city selection
        $(document).on('click', '.city-dropdown-item', function() {
            const cityId = $(this).data('id');
            const cityName = $(this).data('name');
            
            $("#city_search").val(cityName);
            $("#city_id").val(cityId);
            $("#city_dropdown").hide();
            
            // Show selected info
            $("#selected_id").text(cityId);
            $("#selected_name").text(cityName);
            $("#selected_info").show();
        });
        
        // Hide dropdown when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.form-group').length) {
                $("#city_dropdown").hide();
            }
        });
    </script>
</body>
</html>
