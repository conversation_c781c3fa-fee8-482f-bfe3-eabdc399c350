<?php
session_start();
include('../admin/lib/db_connection.php');
include('../../mail.php');
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

$sel = dbQuery("SELECT * FROM tabl_user WHERE email='" . $_REQUEST['email'] . "'");
$num = dbNumRows($sel);

if ($num > 0) {
	echo 2;
	die();
} else {

	dbQuery("INSERT INTO tabl_user SET name='" . $_REQUEST['name'] . "',phone='" . mysqli_real_escape_string($con, $_REQUEST['phone']) . "',email='" . mysqli_real_escape_string($con, $_REQUEST['email']) . "',user_image='default.png',status=1,date_added='" . $date . "'");
	$new_id = dbInsertId();

	$to = $_REQUEST['email'];
	// Subject
	$subject = 'Welcome to Project Womaniya';
	// Message
	$message = '<html>
<body>
<h1>Project Womaniya</h1> 
<div class="text" style="padding: 0 3em;">
<h2>Welcome to Project Womaniya!!</h2>
<p>Hello ' . ucfirst($_REQUEST['name']) . ',<br/>
Congratulations!!<br/>You\'ve successfully completed the registration. </p> 
<p>Regards<br/>
Team Project Womaniya</p>
</div>
</html>';
	// To send HTML mail, the Content-type header must be set
	$headers[] = 'MIME-Version: 1.0';
	$headers[] = 'Content-type: text/html; charset=iso-8859-1';
	// Additional headers
	$headers[] = 'From: ' . SITE . ' <' . EMAIL . '>';
	// Mail it
	// mail($to, $subject, $message, implode("\r\n", $headers));
	sendmail($to, $subject, $message);
	echo 1;
}
