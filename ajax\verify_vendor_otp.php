<?php
session_start();
include('../admin/lib/db_connection.php');
include('../../mail.php');
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

$otp = $_REQUEST['digit-2'] . '' . $_REQUEST['digit-3'] . '' . $_REQUEST['digit-4'] . '' . $_REQUEST['digit-5'];
$sel = dbQuery("SELECT * FROM tabl_vendor_otp WHERE otp='" . $otp . "' AND email='" . $_SESSION['vendor_login_email'] . "'");
$num = dbNumRows($sel);
if ($num == 0) {
  echo 2;
  die();
} else {

  $sel1 = dbQuery("SELECT * FROM tabl_temp_vendor WHERE email='" . $_SESSION['vendor_login_email'] . "'");
  $res1 = dbFetchAssoc($sel1);

  dbQuery("INSERT INTO tabl_delivery_vendor SET name='" . mysqli_real_escape_string($con, $res1['name']) . "',phone='" . mysqli_real_escape_string($con, $res1['phone']) . "',password='" . $res1['password'] . "',email='" . mysqli_real_escape_string($con, $res1['email']) . "',city_id='" . $res1['city_id'] . "',user_image='default.png',status=1,date_added='" . $date . "'");

  dbQuery("DELETE FROM tabl_temp_vendor WHERE email='" . $_SESSION['vendor_login_email'] . "'");
  dbQuery("DELETE FROM tabl_vendor_otp WHERE email='" . $_SESSION['vendor_login_email'] . "'");

  $to = $res1['email'];
  $subject = 'Vendor Registration | Project Womaniya';
  $message = '<html>
<body>
<h1>Project Womaniya</h1>
<div class="text" style="padding: 0 3em;">
<h2>You have successfully registered with project womaniya Shopping.</h2>
<p>Regards<br/>
Team Project Womaniya</p>
</div>
</html>';
  $headers[] = 'MIME-Version: 1.0';
  $headers[] = 'Content-type: text/html; charset=iso-8859-1';
  $headers[] = 'From: ' . SITE . ' <' . EMAIL . '>';
  // mail($to, $subject, $message, implode("\r\n", $headers));
  sendmail($to, $subject, $message);
  echo 1;
}
