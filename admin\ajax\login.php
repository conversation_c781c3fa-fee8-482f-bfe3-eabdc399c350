<?php
session_start();
include('../lib/db_connection.php');
$user = $_REQUEST['user'];
$password = md5($_REQUEST['password']);

$query = dbQuery("SELECT * FROM  `tabl_delivery_admin` WHERE  `user` =  '" . $user . "' AND PASSWORD =  '" . $password . "'");

$result = dbFetchAssoc($query);
$num_rows = dbNumRows($query);
if ($num_rows > 0) {
    $_SESSION["user"] = $result['user'];
    $_SESSION["name"] = $result['name'];
    $_SESSION["user_id"] = $result['id'];
    echo '1';
} else {
    echo '2';
} ?>