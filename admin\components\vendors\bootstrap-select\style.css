/*  BOOTSTRAP SELECT */
.bootstrap-select .btn-default {
    color: #786fa4 !important;
    background-color: #fff;
    border-color: #e4e9f0;
}

.bootstrap-select .btn-default:hover {
    border-color: #dde2ec;
    background: #fff !important;
}

.bootstrap-select .btn-default:focus, .bootstrap-select .btn-default:active {
    border-color: #1b55e3 !important;
    background: #fff !important;
    color: #786fa4 !important;
}

.bootstrap-select.show .btn-default {
    border-color: #1b55e3 !important;
    background: #fff !important;
    color: #786fa4 !important;
}

.bootstrap-select .dropdown-menu li a {
    padding: 0.33rem 1rem;
    outline: none;
    display: block;
}

.bootstrap-select .dropdown-menu li a:hover {
    color: #1b55e3;
}

.bootstrap-select .dropdown-menu li a.opt {
    padding-left: 1rem !important;
}

.bootstrap-select .dropdown-menu li.hidden {
    display: none;
}

.bootstrap-select .dropdown-menu li.no-results {
    margin-left: 0.53rem;
    margin-right: 0.53rem;
    padding: 0.53rem 0.66rem;
    -webkit-border-radius: 5px;
            border-radius: 5px;
    background: #f2f4f8;
    color: #786fa4;
}

.bootstrap-select .filter-option {
    -o-text-overflow: ellipsis;
       text-overflow: ellipsis;
}

.bootstrap-select .dropdown-toggle:focus {
    outline: none !important;
}
