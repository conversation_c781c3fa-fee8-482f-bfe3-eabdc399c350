<?php 
session_start();
include('../admin/lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");
$date_time=date('Y-m-d H:i:s');
$date=date('Y-m-d');
$session_id=session_id();
$qty=$_REQUEST['qty_val'];

$sel=dbQuery("SELECT * FROM tabl_cart WHERE id='".$_REQUEST['cid_val']."' AND session_id='".$session_id."'");
$res=dbFetchAssoc($sel);

$price=$res['price'];
$total_price=$qty*$price;



dbQuery("UPDATE tabl_cart SET qty='".$qty."',total_price='".$total_price."' WHERE id='".$_REQUEST['cid_val']."' AND session_id='".$session_id."'");



$sel2=dbQuery("SELECT sum(total_price) as ttl_price FROM tabl_cart WHERE session_id='".$session_id."'");
$res2=dbFetchAssoc($sel2);


$promo=dbQuery("SELECT * FROM tabl_cart_promo WHERE session_id='".$session_id."'");
$res_promo=dbFetchAssoc($promo);

$sel1=dbQuery("SELECT * FROM `tabl_promo_code` WHERE id='".$res_promo['promo_code']."' AND DATE(end_date)>='".$date."'");
$res1=dbFetchAssoc($sel1);

if($coupon_num=dbNumRows($sel1)>0){  

if($res1['type']==1){
    $discount=$res1['value'];
 }else{
	 $discount=($res2['ttl_price']*$res1['value']/100);
  } 
 dbQuery("DELETE FROM tabl_cart_promo WHERE session_id='".$session_id."'");  
dbQuery("INSERT INTO tabl_cart_promo SET session_id='".$session_id."',promo_code='".$res1['id']."',discount_price='".$discount."'"); 
   }
 echo 1;
 ?>