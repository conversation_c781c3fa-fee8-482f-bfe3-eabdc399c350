<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Payment Settings</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .payment-info {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .qr-code {
            text-align: center;
            margin: 20px 0;
        }
        
        .qr-code img {
            max-width: 250px;
            border: 2px solid #007bff;
            border-radius: 10px;
            padding: 10px;
        }
        
        .upi-info {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .site-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Payment Settings Test</h1>
        
        <?php
        // Include database connection
        include('admin/lib/db_connection.php');
        
        // Fetch settings
        $sel = dbQuery("SELECT * FROM tabl_delivery_setting WHERE id='1'");
        $res = dbFetchAssoc($sel);
        
        if($res) {
        ?>
        
        <div class="site-info">
            <h3>Site Information</h3>
            <p><strong>Site Name:</strong> <?php echo htmlspecialchars($res['site_name']); ?></p>
            <p><strong>Site Email:</strong> <?php echo htmlspecialchars($res['site_email']); ?></p>
        </div>
        
        <div class="payment-info">
            <h3>Payment Information</h3>
            
            <?php if(!empty($res['upi_id'])): ?>
            <div class="upi-info">
                <h4>UPI Payment</h4>
                <p><strong>UPI ID:</strong> <?php echo htmlspecialchars($res['upi_id']); ?></p>
                <p><small>You can use this UPI ID for manual payments</small></p>
            </div>
            <?php else: ?>
            <div class="upi-info">
                <p><em>UPI ID not configured</em></p>
            </div>
            <?php endif; ?>
            
            <?php if(!empty($res['qr_code'])): ?>
            <div class="qr-code">
                <h4>QR Code for Payment</h4>
                <img src="assets/images/qr_code/<?php echo $res['qr_code']; ?>" alt="Payment QR Code">
                <p><small>Scan this QR code to make payment</small></p>
            </div>
            <?php else: ?>
            <div class="qr-code">
                <p><em>QR Code not uploaded</em></p>
            </div>
            <?php endif; ?>
        </div>
        
        <?php
        } else {
            echo '<p>No settings found. Please configure settings in admin panel.</p>';
        }
        ?>
        
        <div style="margin-top: 30px; text-align: center;">
            <a href="admin/setting.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                Go to Admin Settings
            </a>
        </div>
    </div>
</body>
</html>
