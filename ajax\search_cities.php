<?php
include('../admin/lib/db_connection.php');

// Handle both old format (search) and new format (query) for backward compatibility
$search_term = '';
if(isset($_POST['query'])) {
    $search_term = $_POST['query'];
} elseif(isset($_POST['search'])) {
    $search_term = $_POST['search'];
}

if(!empty($search_term)) {
    $search = mysqli_real_escape_string($con, $search_term);

    // For vendor search - only show cities that have vendors
    if(isset($_POST['query'])) {
        $cities_query = dbQuery("SELECT DISTINCT tabl_city.city, tabl_state.state_name
                               FROM tabl_city
                               INNER JOIN tabl_state ON tabl_city.state_id = tabl_state.id
                               INNER JOIN tabl_delivery_vendor ON tabl_delivery_vendor.city_id = tabl_city.id
                               WHERE (tabl_city.city LIKE '%".$search."%' OR tabl_state.state_name LIKE '%".$search."%')
                               AND tabl_city.status = 1 AND tabl_state.status = 1
                               AND tabl_delivery_vendor.status = 1
                               ORDER BY tabl_city.city ASC
                               LIMIT 10");

        if(dbNumRows($cities_query) > 0) {
            while($city = dbFetchAssoc($cities_query)) {
                echo '<div class="city-suggestion">' . htmlspecialchars($city['city'] . ', ' . $city['state_name']) . '</div>';
            }
        } else {
            echo '<div class="city-suggestion" style="color: #666; font-style: italic;">No cities found</div>';
        }
    } else {
        // Original format for backward compatibility
        $query = "SELECT tabl_city.id, tabl_city.city, tabl_state.state_name
                  FROM tabl_city
                  INNER JOIN tabl_state ON tabl_city.state_id = tabl_state.id
                  WHERE tabl_city.status = 1
                  AND tabl_state.status = 1
                  AND (tabl_city.city LIKE '%".$search."%' OR tabl_state.state_name LIKE '%".$search."%')
                  ORDER BY tabl_city.city ASC
                  LIMIT 10";

        $result = dbQuery($query);
        $cities = array();

        while($row = dbFetchAssoc($result)){
            $cities[] = array(
                'id' => $row['id'],
                'city' => $row['city'],
                'state_name' => $row['state_name']
            );
        }

        echo json_encode($cities);
    }
} else {
    if(isset($_POST['query'])) {
        echo '';
    } else {
        echo json_encode(array());
    }
}
?>
