/*!
 * jQuery Form Validation
 * Copyright (C) 2015 RunningCoder.org
 * Licensed under the MIT license
 *
 * <AUTHOR>
 * @version 1.5.3 (2015-07-16)
 * @link http://www.runningcoder.org/jqueryvalidation/
*/
!function(window,document,$,undefined){function _buildRegexFromString(a){function b(){}if(!a||"string"!=typeof a&&!(a instanceof RegExp))return b(),!1;"string"!=typeof a&&(a=a.toString());for(var c,d,e,f=a.charAt(0),g=a.length-1;g>0&&/[gimsxeU]/.test(a.charAt(g));)g--;a.charAt(g)!==f&&(f=null),f&&g!==a.length-1&&(d=a.substr(g+1,a.length-1)),c=f?a.substr(1,g-1):a;try{e=new RegExp(c,d)}catch(h){return b(),!1}return e}function isEmpty(a){for(var b in a)if(a.hasOwnProperty(b))return!1;return!0}window.Validation={form:[],labels:{},hasScrolled:!1},"function"!=typeof Object.preventExtensions&&(Object.preventExtensions=function(a){return a});var _rules={NOTEMPTY:/\S/,INTEGER:/^\d+$/,NUMERIC:/^\d+(?:[,\s]\d{3})*(?:\.\d+)?$/,MIXED:/^[\w\s-]+$/,NAME:/^['a-zãàáäâẽèéëêìíïîõòóöôùúüûñç\s-]+$/i,NOSPACE:/^(?!\s)\S*$/,TRIM:/^[^\s].*[^\s]$/,DATE:/^\d{4}-\d{2}-\d{2}(\s\d{2}:\d{2}(:\d{2})?)?$/,EMAIL:/^([^@]+?)@(([a-z0-9]-*)*[a-z0-9]+\.)+([a-z0-9]+)$/i,URL:/^(https?:\/\/)?((([a-z0-9]-*)*[a-z0-9]+\.?)*([a-z0-9]+))(\/[\w?=\.-]*)*$/,PHONE:/^(\()?\d{3}(\))?(-|\s)?\d{3}(-|\s)\d{4}$/,OPTIONAL:/\S/,COMPARISON:/^\s*([LV])\s*([<>]=?|==|!=)\s*([^<>=!]+?)\s*$/},_messages={"default":"$ contain error(s).",NOTEMPTY:"$ must not be empty.",INTEGER:"$ must be an integer.",NUMERIC:"$ must be numeric.",MIXED:"$ must be letters or numbers (no special characters).",NAME:"$ must not contain special characters.",NOSPACE:"$ must not contain spaces.",TRIM:"$ must not start or end with space character.",DATE:"$ is not a valid with format YYYY-MM-DD.",EMAIL:"$ is not valid.",URL:"$ is not valid.",PHONE:"$ is not a valid phone number.","<":"$ must be less than % characters.","<=":"$ must be less or equal to % characters.",">":"$ must be greater than % characters.",">=":"$ must be greater or equal to % characters.","==":"$ must be equal to %","!=":"$ must be different than %"},_data={validation:"data-validation",validationMessage:"data-validation-message",regex:"data-validation-regex",regexReverse:"data-validation-regex-reverse",regexMessage:"data-validation-regex-message",group:"data-validation-group",label:"data-validation-label",errorList:"data-error-list"},_options={submit:{settings:{form:null,display:"inline",insertion:"append",allErrors:!1,trigger:"click",button:"[type='submit']",errorClass:"error",errorListClass:"error-list",errorListContainer:null,inputContainer:null,clear:"focusin",scrollToError:!1},callback:{onInit:null,onValidate:null,onError:null,onBeforeSubmit:null,onSubmit:null,onAfterSubmit:null}},dynamic:{settings:{trigger:null,delay:300},callback:{onSuccess:null,onError:null,onComplete:null}},rules:{},messages:{},labels:{},debug:!1},_supported={submit:{settings:{display:["inline","block"],insertion:["append","prepend"],allErrors:[!0,!1],clear:["focusin","keypress",!1],trigger:["click","dblclick","focusout","hover","mousedown","mouseenter","mouseleave","mousemove","mouseout","mouseover","mouseup","toggle"]}},dynamic:{settings:{trigger:["focusout","keydown","keypress","keyup"]}},debug:[!0,!1]},Validation=function(node,options){function extendRules(){options.rules=$.extend(!0,{},_rules,options.rules)}function extendMessages(){options.messages=$.extend(!0,{},_messages,options.messages)}function extendOptions(){options instanceof Object||(options={});var a=Object.preventExtensions($.extend(!0,{},_options));for(var b in options)if(options.hasOwnProperty(b)&&"debug"!==b)if(~["labels","messages","rules"].indexOf(b)&&options[b]instanceof Object)a[b]=options[b];else if(_options[b]&&options[b]instanceof Object)for(var c in options[b])if(options[b].hasOwnProperty(c)&&_options[b][c]&&options[b][c]instanceof Object){for(var d in options[b][c])options[b][c].hasOwnProperty(d)&&_supported[b]&&_supported[b][c]&&_supported[b][c][d]&&-1===$.inArray(options[b][c][d],_supported[b][c][d])&&delete options[b][c][d];a[b]&&a[b][c]&&(a[b][c]=$.extend(Object.preventExtensions(a[b][c]),options[b][c]))}a.dynamic.settings.trigger&&"keypress"===a.dynamic.settings.trigger&&"keypress"===a.submit.settings.clear&&(a.dynamic.settings.trigger="keydown"),options=a}function delegateDynamicValidation(){if(!options.dynamic.settings.trigger)return!1;if(!node.find("["+_data.validation+"],["+_data.regex+"]")[0])return!1;var a=options.dynamic.settings.trigger+delegateSuffix;"focusout"!==options.dynamic.settings.trigger&&(a+=" change"+delegateSuffix+" paste"+delegateSuffix),$.each(node.find("["+_data.validation+"],["+_data.regex+"]"),function(b,c){$(c).unbind(a).on(a,function(a){if($(this).is(":disabled"))return!1;var b=this,c=a.keyCode||null;_typeWatch(function(){validateInput(b)?_executeCallback(options.dynamic.callback.onSuccess,[node,b,c]):(displayOneError(b.name),_executeCallback(options.dynamic.callback.onError,[node,b,c,errors[b.name]])),_executeCallback(options.dynamic.callback.onComplete,[node,b,c])},options.dynamic.settings.delay)})})}function delegateValidation(){_executeCallback(options.submit.callback.onInit,[node]);var a=options.submit.settings.trigger+".vd";return node.find(options.submit.settings.button)[0]?(node.on("submit",!1),void node.find(options.submit.settings.button).off(".vd").on(a,function(a){return a.preventDefault(),resetErrors(),_executeCallback(options.submit.callback.onValidate,[node]),validateForm()?(_executeCallback(options.submit.callback.onBeforeSubmit,[node]),options.submit.callback.onSubmit?_executeCallback(options.submit.callback.onSubmit,[node,formData]):submitForm(),_executeCallback(options.submit.callback.onAfterSubmit,[node])):(displayErrors(),_executeCallback(options.submit.callback.onError,[node,errors,formData])),!1})):!1}function validateForm(){var a=isEmpty(errors);return formData={},$.each(node.find('input:not([type="submit"]), select, textarea').not(":disabled"),function(b,c){c=$(c);var d=_getInputValue(c[0]),e=c.attr("name");e&&(/\[]$/.test(e)?(e=e.replace(/\[]$/,""),formData[e]instanceof Array||(formData[e]=[]),formData[e].push(d)):formData[e]=d),(c.attr(_data.validation)||c.attr(_data.regex))&&(validateInput(c[0],d)||(a=!1))}),prepareFormData(),a}function prepareFormData(){var a,b,c={};for(var d in formData)if(formData.hasOwnProperty(d)){b=0,a=d.split(/\[(.+?)]/g);for(var e={},f=[],g=a.length-1;g>=0;g--)""!==a[g]?(f.length<1?e[a[g]]=Number(formData[d])||formData[d]:(e={},e[a[g]]=f[f.length-1]),f.push(e)):a.splice(g,1);c=$.extend(!0,c,e)}formData=c}function validateInput(a,b){var c=$(a).attr("name"),b=b||_getInputValue(a);if(!c)return!1;var d=c.replace(/]$/,"").split(/]\[|[[\]]/g),e=window.Validation.labels[c]||options.labels[c]||$(a).attr(_data.label)||d[d.length-1],f=$(a).attr(_data.validation),g=$(a).attr(_data.validationMessage),h=$(a).attr(_data.regex),i=!($(a).attr(_data.regexReverse)===undefined),j=$(a).attr(_data.regexMessage),k=!1;if(f&&(f=_api._splitValidation(f)),f instanceof Array&&f.length>0){if(""===$.trim(b)&&~f.indexOf("OPTIONAL"))return!0;$.each(f,function(a,d){if(k===!0)return!0;try{validateRule(b,d)}catch(f){(g||!options.submit.settings.allErrors)&&(k=!0),f[0]=g||f[0],registerError(c,f[0].replace("$",e).replace("%",f[1]))}})}if(h){var l=_buildRegexFromString(h);if(!(l instanceof RegExp))return!0;try{validateRule(b,l,i)}catch(m){m[0]=j||m[0],registerError(c,m[0].replace("$",e))}}return!errors[c]||errors[c]instanceof Array&&0===errors[c].length}function validateRule(value,rule,reversed){if(rule instanceof RegExp){var isValid=rule.test(value);if(reversed&&(isValid=!isValid),!isValid)throw[options.messages["default"],""]}else if(options.rules[rule]){if(!options.rules[rule].test(value))throw[options.messages[rule],""]}else{var comparison=rule.match(options.rules.COMPARISON);if(comparison&&4===comparison.length){var type=comparison[1],operator=comparison[2],compared=comparison[3],comparedValue;switch(type){case"L":if(isNaN(compared))return!1;if(!value||eval(value.length+operator+parseFloat(compared))===!1)throw[options.messages[operator],compared];break;case"V":default:if(isNaN(compared)){if(comparedValue=node.find('[name="'+compared+'"]').val(),!comparedValue)return!1;if(!value||!eval('"'+encodeURIComponent(value)+'"'+operator+'"'+encodeURIComponent(comparedValue)+'"'))throw[options.messages[operator].replace(" characters",""),compared]}else if(!value||isNaN(value)||!eval(value+operator+parseFloat(compared)))throw[options.messages[operator].replace(" characters",""),compared]}}}}function registerError(a,b){errors[a]||(errors[a]=[]),b=b.capitalize();for(var c=!1,d=0;d<errors[a].length;d++)if(errors[a][d]===b){c=!0;break}c||errors[a].push(b)}function displayOneError(a){var b,c,d,e,f,g,h='<div class="'+options.submit.settings.errorListClass+'" '+_data.errorList+"><ul></ul></div>";if(!errors.hasOwnProperty(a))return!1;if(b=node.find('[name="'+a+'"]'),e=null,!b[0])return!1;if(f=b.attr(_data.group),f?(g=node.find('[name="'+a+'"]'),e=node.find('[id="'+f+'"]'),e[0]&&(e.addClass(options.submit.settings.errorClass),d=e)):(b.addClass(options.submit.settings.errorClass),options.submit.settings.inputContainer&&b.parentsUntil(node,options.submit.settings.inputContainer).addClass(options.submit.settings.errorClass),c=b.attr("id"),c&&(e=node.find('label[for="'+c+'"]')[0]),e||(e=b.parentsUntil(node,"label")[0]),e&&(e=$(e),e.addClass(options.submit.settings.errorClass))),"inline"===options.submit.settings.display?d=options.submit.settings.errorListContainer?b.parentsUntil(node,options.submit.settings.errorListContainer):d||b.parent():"block"===options.submit.settings.display&&(d=node),"inline"===options.submit.settings.display&&d.find("["+_data.errorList+"]")[0])return!1;("inline"===options.submit.settings.display||"block"===options.submit.settings.display&&!d.find("["+_data.errorList+"]")[0])&&("append"===options.submit.settings.insertion?d.append(h):"prepend"===options.submit.settings.insertion&&d.prepend(h));for(var i=0;i<errors[a].length;i++)d.find("["+_data.errorList+"] ul").append("<li>"+errors[a][i]+"</li>");if(options.submit.settings.clear||options.dynamic.settings.trigger){f&&g&&(b=g);var j="coucou"+resetSuffix;options.submit.settings.clear&&(j+=" "+options.submit.settings.clear+resetSuffix,~["radio","checkbox"].indexOf(b[0].type)&&(j+=" change"+resetSuffix)),options.dynamic.settings.trigger&&(j+=" "+options.dynamic.settings.trigger+resetSuffix,"focusout"===options.dynamic.settings.trigger||~["radio","checkbox"].indexOf(b[0].type)||(j+=" change"+resetSuffix+" paste"+resetSuffix)),b.unbind(j).on(j,function(a,b,c,d,e){return function(){e?$(c).hasClass(options.submit.settings.errorClass)&&resetOneError(a,b,c,d,e):$(b).hasClass(options.submit.settings.errorClass)&&resetOneError(a,b,c,d)}}(a,b,e,d,f))}if(options.submit.settings.scrollToError&&!window.Validation.hasScrolled){window.Validation.hasScrolled=!0;var k=parseFloat(options.submit.settings.scrollToError.offset)||0,l=parseFloat(options.submit.settings.scrollToError.duration)||500,m="block"===options.submit.settings.display?d:b;$("html, body").animate({scrollTop:m.offset().top+k},l)}}function displayErrors(){for(var a in errors)errors.hasOwnProperty(a)&&displayOneError(a)}function resetOneError(a,b,c,d,e){if(delete errors[a],d)options.submit.settings.inputContainer&&(e?c:b).parentsUntil(node,options.submit.settings.inputContainer).removeClass(options.submit.settings.errorClass),c&&c.removeClass(options.submit.settings.errorClass),b.removeClass(options.submit.settings.errorClass),"inline"===options.submit.settings.display&&d.find("["+_data.errorList+"]").remove();else{if(!b&&(b=node.find('[name="'+a+'"]'),!b[0]))return!1;b.trigger("coucou"+resetSuffix)}}function resetErrors(){errors=[],window.Validation.hasScrolled=!1,node.find("["+_data.errorList+"]").remove(),node.find("."+options.submit.settings.errorClass).removeClass(options.submit.settings.errorClass)}function submitForm(){node[0].submit()}function destroy(){return resetErrors(),node.find("["+_data.validation+"],["+_data.regex+"]").off(delegateSuffix+" "+resetSuffix),node.find(options.submit.settings.button).off(delegateSuffix).on("click"+delegateSuffix,function(){$(this).closest("form")[0].submit()}),!0}var errors=[],messages={},formData={},delegateSuffix=".vd",resetSuffix=".vr";window.Validation.hasScrolled=!1;var _getInputValue=function(a){var b;switch($(a).attr("type")){case"checkbox":b=$(a).is(":checked")?1:"";break;case"radio":b=node.find('input[name="'+$(a).attr("name")+'"]:checked').val()||"";break;default:b=$(a).val()}return b},_typeWatch=function(){var a=0;return function(b,c){clearTimeout(a),a=setTimeout(b,c)}}(),_executeCallback=function(a,b){if(!a)return!1;var c;if("function"==typeof a)c=a;else if("string"==typeof a||a instanceof Array){c=window,"string"==typeof a&&(a=[a,[]]);for(var d=a[0].split("."),e=a[1],f=!0,g=0;g<d.length;){if("undefined"==typeof c){f=!1;break}c=c[d[g++]]}if(!f||"function"!=typeof c)return!1}return c.apply(this,$.merge(e||[],b?b:[])),!0};return this.__construct=function(){extendOptions(),extendRules(),extendMessages(),delegateDynamicValidation(),delegateValidation()}(),{registerError:registerError,displayOneError:displayOneError,displayErrors:displayErrors,resetOneError:resetOneError,resetErrors:resetErrors,destroy:destroy}};$.fn.validate=$.validate=function(a){return _api.validate(this,a)},$.fn.addValidation=function(a){return _api.addValidation(this,a)},$.fn.removeValidation=function(a){return _api.removeValidation(this,a)},$.fn.addError=function(a){return _api.addError(this,a)},$.fn.removeError=function(a){return _api.removeError(this,a)},$.fn.alterValidationRules=$.alterValidationRules=function(a){a instanceof Array||(a=[a]);for(var b=0;b<a.length;b++)_api.alterValidationRules(a[b])};var _api={_formatValidation:function(a){return a=a.toString().replace(/\s/g,""),"["===a.charAt(0)&&"]"===a.charAt(a.length-1)&&(a=a.replace(/^\[|]$/g,"")),a},_splitValidation:function(a){for(var b,c=this._formatValidation(a).split(","),d=0;d<c.length;d++)b=c[d],/^[a-z]+$/i.test(b)&&(c[d]=b.toUpperCase());return c},_joinValidation:function(a){return"["+a.join(", ")+"]"},validate:function(a,b){if("function"==typeof a){if(!b.submit.settings.form)return;if(a=$(b.submit.settings.form),!a[0]||"form"!==a[0].nodeName.toLowerCase())return}else if("undefined"==typeof a[0])return;if("destroy"===b){if(!window.Validation.form[a.selector])return;return void window.Validation.form[a.selector].destroy()}return a.each(function(){window.Validation.form[a.selector]=new Validation($(this),b)})},addValidation:function(a,b){var c=this;return b=c._splitValidation(b),b?a.each(function(){for(var a,d=$(this),e=d.attr(_data.validation),f=e&&e.length?c._splitValidation(e):[],g=0;g<b.length;g++)a=c._formatValidation(b[g]),-1===$.inArray(a,f)&&f.push(a);f.length&&d.attr(_data.validation,c._joinValidation(f))}):!1},removeValidation:function(a,b){var c=this;return b=c._splitValidation(b),b?a.each(function(){var a,d,e=$(this),f=e.attr(_data.validation),g=f&&f.length?c._splitValidation(f):[];if(!g.length)return e.removeAttr(_data.validation),!0;for(var h=0;h<b.length;h++)a=c._formatValidation(b[h]),d=$.inArray(a,g),-1!==d&&g.splice(d,1);return g.length?void e.attr(_data.validation,c._joinValidation(g)):(e.removeAttr(_data.validation),!0)}):!1},addError:function(a,b){if(!window.Validation.form[a.selector])return!1;if("object"!=typeof b||"[object Object]"!==Object.prototype.toString.call(b))return!1;var c,d=!0;for(var e in b)if(b.hasOwnProperty(e)&&(b[e]instanceof Array||(b[e]=[b[e]]),c=$(a.selector).find('[name="'+e+'"]'),c[0])){d&&(window.Validation.hasScrolled=!1,d=!1),window.Validation.form[a.selector].resetOneError(e,c);for(var f=0;f<b[e].length;f++)"string"==typeof b[e][f]&&window.Validation.form[a.selector].registerError(e,b[e][f]);window.Validation.form[a.selector].displayOneError(e)}},removeError:function(a,b){if(!window.Validation.form[a.selector])return!1;if(!b)return window.Validation.form[a.selector].resetErrors(),!1;if("object"==typeof b&&"[object Array]"!==Object.prototype.toString.call(b))return!1;b instanceof Array||(b=[b]);for(var c,d=0;d<b.length;d++)c=$(a.selector).find('[name="'+b[d]+'"]'),c[0]&&window.Validation.form[a.selector].resetOneError(b[d],c)},alterValidationRules:function(a){if(!a.rule||!a.regex&&!a.message)return!1;if(a.rule=a.rule.toUpperCase(),a.regex){var b=_buildRegexFromString(a.regex);if(!(b instanceof RegExp))return!1;_rules[a.rule]=b}return a.message&&(_messages[a.rule]=a.message),!0}};String.prototype.capitalize=function(){return this.charAt(0).toUpperCase()+this.slice(1)},Array.prototype.indexOf||(Array.prototype.indexOf=function(a){var b=this.length>>>0,c=Number(arguments[1])||0;for(c=0>c?Math.ceil(c):Math.floor(c),0>c&&(c+=b);b>c;c++)if(c in this&&this[c]===a)return c;return-1})}(window,document,window.jQuery);