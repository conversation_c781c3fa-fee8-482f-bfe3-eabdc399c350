<?php 
session_start();
include('admin/lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");

// Get vendor ID from URL
$vendor_id = isset($_GET['id']) ? $_GET['id'] : '';

if(empty($vendor_id)) {
    echo '<script>window.location.href="vendors.php"</script>';
    exit;
}

// Get vendor details
$vendor_query = dbQuery("SELECT tabl_delivery_vendor.*, tabl_city.city, tabl_state.state_name 
                        FROM tabl_delivery_vendor 
                        LEFT JOIN tabl_city ON tabl_delivery_vendor.city_id = tabl_city.id 
                        LEFT JOIN tabl_state ON tabl_city.state_id = tabl_state.id 
                        WHERE tabl_delivery_vendor.id = '".$vendor_id."' AND tabl_delivery_vendor.status = 1");

$vendor_exists = dbNumRows($vendor_query);

if($vendor_exists == 0) {
    echo '<script>window.location.href="vendors.php"</script>';
    exit;
}

$vendor = dbFetchAssoc($vendor_query);

// Get vendor's products count
$products_count_query = dbQuery("SELECT COUNT(*) as total FROM tabl_products WHERE vendor_id = '".$vendor_id."' AND status = 1");
$products_count = dbFetchAssoc($products_count_query)['total'];

// Get vendor's categories
$categories_query = dbQuery("SELECT DISTINCT tabl_category.category 
                           FROM tabl_products 
                           INNER JOIN tabl_category ON tabl_products.category_id = tabl_category.id 
                           WHERE tabl_products.vendor_id = '".$vendor_id."' AND tabl_products.status = 1 
                           LIMIT 5");
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <title><?php echo htmlspecialchars($vendor['name']); ?> - Project Womaniya</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, minimal-ui, viewport-fit=cover">
  <meta name="theme-color" content="#009688">
  <link rel="shortcut icon" type="image/x-icon" href="assets/images/favicon.png">
  <link rel="stylesheet" type="text/css" href="assets/css/style.css">
  <link rel="preconnect" href="https://fonts.googleapis.com/">
  <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&amp;family=Poppins:wght@200;300;400;500;600;700;800;900&amp;display=swap" rel="stylesheet">
  
  <style>
    .vendor-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 30px 20px;
      color: white;
      text-align: center;
      border-radius: 0 0 25px 25px;
      margin-bottom: 20px;
    }
    
    .vendor-avatar-large {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      object-fit: cover;
      border: 4px solid white;
      margin-bottom: 15px;
    }
    
    .vendor-name {
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 5px;
    }
    
    .vendor-location {
      font-size: 16px;
      opacity: 0.9;
      margin-bottom: 15px;
    }
    
    .vendor-stats {
      display: flex;
      justify-content: center;
      gap: 30px;
      margin-top: 20px;
    }
    
    .stat-item {
      text-align: center;
    }
    
    .stat-number {
      font-size: 20px;
      font-weight: 700;
      display: block;
    }
    
    .stat-label {
      font-size: 12px;
      opacity: 0.8;
    }
    
    .info-card {
      background: white;
      border-radius: 15px;
      padding: 20px;
      margin-bottom: 15px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .info-card h6 {
      color: #333;
      font-weight: 600;
      margin-bottom: 15px;
      display: flex;
      align-items: center;
    }
    
    .info-card h6 i {
      margin-right: 8px;
      color: #007bff;
    }
    
    .contact-item {
      display: flex;
      align-items: center;
      padding: 10px 0;
      border-bottom: 1px solid #f0f0f0;
    }
    
    .contact-item:last-child {
      border-bottom: none;
    }
    
    .contact-icon {
      width: 40px;
      height: 40px;
      background: #f8f9fa;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      color: #007bff;
    }
    
    .contact-info {
      flex-grow: 1;
    }
    
    .contact-label {
      font-size: 12px;
      color: #666;
      margin-bottom: 2px;
    }
    
    .contact-value {
      font-weight: 500;
      color: #333;
    }
    
    .contact-action {
      background: #007bff;
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 20px;
      font-size: 12px;
      text-decoration: none;
    }
    
    .category-tag {
      display: inline-block;
      background: #e3f2fd;
      color: #1976d2;
      padding: 5px 12px;
      border-radius: 15px;
      font-size: 12px;
      margin-right: 8px;
      margin-bottom: 8px;
    }
    
    .action-buttons {
      display: flex;
      gap: 10px;
      margin-top: 20px;
    }
    
    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      padding: 12px 25px;
      border-radius: 25px;
      color: white;
      text-decoration: none;
      font-weight: 600;
      flex: 1;
      text-align: center;
    }
    
    .btn-outline {
      background: transparent;
      border: 2px solid #007bff;
      color: #007bff;
      padding: 10px 25px;
      border-radius: 25px;
      text-decoration: none;
      font-weight: 600;
      flex: 1;
      text-align: center;
    }
  </style>
</head>

<body>
  <div class="page-wraper">
    <!-- Preloader -->
    <div id="preloader">
      <div class="loader">
        <div class="load-circle">
          <div></div>
          <div></div>
        </div>
      </div>
    </div>

    <!-- Header -->
    <header class="header">
      <div class="main-bar">
        <div class="container">
          <div class="header-content">
            <div class="left-content">
              <a href="vendors.php" class="back-btn">
                <i class="fa-solid fa-arrow-left"></i>
              </a>
              <h4 class="title mb-0 text-nowrap">Vendor Profile</h4>
            </div>
            <div class="mid-content"></div>
            <div class="right-content">
              <a href="cart.php" class="item-content item-link">
                <i class="fa-solid fa-shopping-cart"></i>
              </a>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Page Content -->
    <div class="page-content">
      
      <!-- Vendor Header -->
      <div class="vendor-header">
        <img src="vendor/components/core/img/avatars/thumb-50/<?php echo !empty($vendor['user_image']) ? $vendor['user_image'] : 'default.png'; ?>" 
             alt="<?php echo htmlspecialchars($vendor['name']); ?>" 
             class="vendor-avatar-large">
        
        <div class="vendor-name"><?php echo htmlspecialchars($vendor['name']); ?></div>
        
        <?php if(!empty($vendor['city'])): ?>
        <div class="vendor-location">
          <i class="fa-solid fa-map-marker-alt me-1"></i>
          <?php echo $vendor['city'].', '.$vendor['state_name']; ?>
        </div>
        <?php endif; ?>
        
        <div class="vendor-stats">
          <div class="stat-item">
            <span class="stat-number"><?php echo $products_count; ?></span>
            <span class="stat-label">Products</span>
          </div>
          <div class="stat-item">
            <span class="stat-number"><?php echo date('Y', strtotime($vendor['date_added'])); ?></span>
            <span class="stat-label">Since</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">
              <i class="fa-solid fa-star" style="color: #ffd700;"></i>
            </span>
            <span class="stat-label">Verified</span>
          </div>
        </div>
      </div>

      <div class="container">
        
        <!-- Contact Information -->
        <div class="info-card">
          <h6><i class="fa-solid fa-address-book"></i>Contact Information</h6>
          
          <?php if(!empty($vendor['phone'])): ?>
          <div class="contact-item">
            <div class="contact-icon">
              <i class="fa-solid fa-phone"></i>
            </div>
            <div class="contact-info">
              <div class="contact-label">Phone Number</div>
              <div class="contact-value"><?php echo $vendor['phone']; ?></div>
            </div>
            <a href="tel:<?php echo $vendor['phone']; ?>" class="contact-action">Call</a>
          </div>
          <?php endif; ?>
          
          <?php if(!empty($vendor['email'])): ?>
          <div class="contact-item">
            <div class="contact-icon">
              <i class="fa-solid fa-envelope"></i>
            </div>
            <div class="contact-info">
              <div class="contact-label">Email Address</div>
              <div class="contact-value"><?php echo $vendor['email']; ?></div>
            </div>
            <a href="mailto:<?php echo $vendor['email']; ?>" class="contact-action">Email</a>
          </div>
          <?php endif; ?>
        </div>
        
        <!-- Categories -->
        <?php if(dbNumRows($categories_query) > 0): ?>
        <div class="info-card">
          <h6><i class="fa-solid fa-tags"></i>Product Categories</h6>
          <?php while($category = dbFetchAssoc($categories_query)): ?>
          <span class="category-tag"><?php echo htmlspecialchars($category['category']); ?></span>
          <?php endwhile; ?>
        </div>
        <?php endif; ?>
        
        <!-- Business Information -->
        <div class="info-card">
          <h6><i class="fa-solid fa-info-circle"></i>Business Information</h6>
          
          <div class="contact-item">
            <div class="contact-icon">
              <i class="fa-solid fa-calendar"></i>
            </div>
            <div class="contact-info">
              <div class="contact-label">Member Since</div>
              <div class="contact-value"><?php echo date('F Y', strtotime($vendor['date_added'])); ?></div>
            </div>
          </div>
          
          <div class="contact-item">
            <div class="contact-icon">
              <i class="fa-solid fa-check-circle"></i>
            </div>
            <div class="contact-info">
              <div class="contact-label">Status</div>
              <div class="contact-value">
                <span style="color: #28a745; font-weight: 600;">
                  <i class="fa-solid fa-circle" style="font-size: 8px; margin-right: 5px;"></i>
                  Active Vendor
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="action-buttons">
          <a href="product-list.php?vendor_id=<?php echo $vendor['id']; ?>" class="btn-primary">
            <i class="fa-solid fa-shopping-bag me-2"></i>View Products
          </a>
          <a href="vendors.php" class="btn-outline">
            <i class="fa-solid fa-store me-2"></i>All Vendors
          </a>
        </div>
        
      </div>
    </div>
  </div>

  <script src="assets/js/jquery.js"></script>
  <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="assets/js/settings.js"></script>
  <script src="assets/js/custom.js"></script>
</body>
</html>
