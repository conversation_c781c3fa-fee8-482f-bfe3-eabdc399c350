<?php 
session_start();
include('../lib/db_connection.php');
if($_REQUEST['val']==1){
$sel=dbQuery("SELECT * FROM tabl_settlement WHERE id='".$_REQUEST['id']."'");
$res=dbFetchAssoc($sel);

$vendor=dbQuery("SELECT * FROM tabl_delivery_vendor WHERE id='".$res['vendor_id']."'");
$res_vendor=dbFetchAssoc($vendor);

$turnover=$res_vendor['turn_over'];
$turnover_amount=($res['amount']*$turnover)/100;
$turnover_amount=number_format((float)$turnover_amount, 2, '.', '');

$settlement_amount=$res['amount']-$turnover_amount;

dbQuery("UPDATE tabl_settlement SET status='".$_REQUEST['val']."',turnover_amount='".$turnover_amount."',turnover='".$turnover."',settlement_amount='".$settlement_amount."' WHERE id='".$_REQUEST['id']."'");
}else{
dbQuery("UPDATE tabl_settlement SET status='".$_REQUEST['val']."',turnover_amount='0.00',turnover='',settlement_amount='0.00' WHERE id='".$_REQUEST['id']."'");	
}
echo '1'; 
?>